import 'package:flutter/foundation.dart';
import '../../shared/models/models.dart';

enum AppStatus {
  initial,
  loading,
  authenticated,
  unauthenticated,
  error,
}

class AppState extends ChangeNotifier {
  AppStatus _status = AppStatus.initial;
  Doctor? _currentDoctor;
  String? _errorMessage;
  bool _isLoading = false;

  // Getters
  AppStatus get status => _status;
  Doctor? get currentDoctor => _currentDoctor;
  String? get errorMessage => _errorMessage;
  bool get isLoading => _isLoading;
  bool get isAuthenticated => _status == AppStatus.authenticated && _currentDoctor != null;

  // Setters
  void setStatus(AppStatus status) {
    _status = status;
    notifyListeners();
  }

  void setCurrentDoctor(Doctor? doctor) {
    _currentDoctor = doctor;
    if (doctor != null) {
      _status = AppStatus.authenticated;
    } else {
      _status = AppStatus.unauthenticated;
    }
    notifyListeners();
  }

  void setError(String? error) {
    _errorMessage = error;
    if (error != null) {
      _status = AppStatus.error;
    }
    notifyListeners();
  }

  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void clearError() {
    _errorMessage = null;
    if (_status == AppStatus.error) {
      _status = _currentDoctor != null ? AppStatus.authenticated : AppStatus.unauthenticated;
    }
    notifyListeners();
  }

  void logout() {
    _currentDoctor = null;
    _status = AppStatus.unauthenticated;
    _errorMessage = null;
    _isLoading = false;
    notifyListeners();
  }

  void reset() {
    _status = AppStatus.initial;
    _currentDoctor = null;
    _errorMessage = null;
    _isLoading = false;
    notifyListeners();
  }
}
