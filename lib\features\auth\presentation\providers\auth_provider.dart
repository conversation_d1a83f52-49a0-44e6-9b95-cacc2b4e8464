import 'package:flutter/foundation.dart';
import '../../../../shared/models/models.dart';
import '../../../../core/providers/app_state.dart';

class AuthProvider extends ChangeNotifier {
  final AppState _appState;
  
  AuthProvider(this._appState);

  // Registration state
  bool _isRegistering = false;
  String? _registrationError;
  VerificationStatus _verificationStatus = VerificationStatus.pending;

  // Login state
  bool _isLoggingIn = false;
  String? _loginError;

  // OTP state
  bool _isOtpSent = false;
  bool _isVerifyingOtp = false;
  String? _otpError;
  int _otpTimeLeft = 0;

  // Document upload state
  bool _isUploadingDocuments = false;
  String? _uploadError;
  final Map<String, bool> _uploadedDocuments = {};

  // Getters
  bool get isRegistering => _isRegistering;
  String? get registrationError => _registrationError;
  VerificationStatus get verificationStatus => _verificationStatus;
  
  bool get isLoggingIn => _isLoggingIn;
  String? get loginError => _loginError;
  
  bool get isOtpSent => _isOtpSent;
  bool get isVerifyingOtp => _isVerifyingOtp;
  String? get otpError => _otpError;
  int get otpTimeLeft => _otpTimeLeft;
  
  bool get isUploadingDocuments => _isUploadingDocuments;
  String? get uploadError => _uploadError;
  Map<String, bool> get uploadedDocuments => _uploadedDocuments;

  // Registration methods
  Future<void> register({
    required String fullName,
    required String email,
    required String phoneNumber,
    required String medicalLicenseNumber,
    required String specialization,
    required int yearsOfExperience,
    required String clinicAddress,
    required List<String> languagesSpoken,
    required String password,
  }) async {
    _isRegistering = true;
    _registrationError = null;
    notifyListeners();

    try {
      // TODO: Implement actual registration API call
      await Future.delayed(const Duration(seconds: 2)); // Simulate API call
      
      // For now, create a mock doctor
      final doctor = Doctor(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        fullName: fullName,
        email: email,
        phoneNumber: phoneNumber,
        medicalLicenseNumber: medicalLicenseNumber,
        specialization: specialization,
        yearsOfExperience: yearsOfExperience,
        clinicAddress: clinicAddress,
        languagesSpoken: languagesSpoken,
        profileImageUrl: '',
        isVerified: false,
        isAvailable: true,
        rating: 0.0,
        totalReviews: 0,
        consultationFee: 500.0,
        qualifications: [],
        workingHours: const WorkingHours(
          schedule: {},
          holidays: [],
          blockedSlots: [],
        ),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        verificationStatus: VerificationStatus.pending,
        badges: [],
      );

      _appState.setCurrentDoctor(doctor);
      _verificationStatus = VerificationStatus.pending;
      
    } catch (e) {
      _registrationError = e.toString();
    } finally {
      _isRegistering = false;
      notifyListeners();
    }
  }

  // Login methods
  Future<void> login({
    required String email,
    required String password,
  }) async {
    _isLoggingIn = true;
    _loginError = null;
    notifyListeners();

    try {
      // TODO: Implement actual login API call
      await Future.delayed(const Duration(seconds: 2)); // Simulate API call
      
      // For now, create a mock verified doctor
      final doctor = Doctor(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        fullName: 'Dr. John Smith',
        email: email,
        phoneNumber: '+91 **********',
        medicalLicenseNumber: 'MED123456',
        specialization: 'Cardiology',
        yearsOfExperience: 10,
        clinicAddress: '123 Medical Center, City',
        languagesSpoken: ['English', 'Hindi'],
        profileImageUrl: '',
        isVerified: true,
        isAvailable: true,
        rating: 4.5,
        totalReviews: 150,
        consultationFee: 800.0,
        bio: 'Experienced cardiologist with 10+ years of practice.',
        qualifications: ['MBBS', 'MD Cardiology'],
        workingHours: const WorkingHours(
          schedule: {},
          holidays: [],
          blockedSlots: [],
        ),
        createdAt: DateTime.now().subtract(const Duration(days: 365)),
        updatedAt: DateTime.now(),
        verificationStatus: VerificationStatus.verified,
        badges: ['Top Rated Doctor', 'Specialist'],
      );

      _appState.setCurrentDoctor(doctor);
      
    } catch (e) {
      _loginError = e.toString();
    } finally {
      _isLoggingIn = false;
      notifyListeners();
    }
  }

  // OTP methods
  Future<void> sendOtp(String phoneNumber) async {
    _isOtpSent = false;
    _otpError = null;
    notifyListeners();

    try {
      // TODO: Implement actual OTP sending API call
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call
      
      _isOtpSent = true;
      _otpTimeLeft = 120; // 2 minutes
      _startOtpTimer();
      
    } catch (e) {
      _otpError = e.toString();
    } finally {
      notifyListeners();
    }
  }

  Future<void> verifyOtp(String otp) async {
    _isVerifyingOtp = true;
    _otpError = null;
    notifyListeners();

    try {
      // TODO: Implement actual OTP verification API call
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call
      
      if (otp == '123456') { // Mock OTP
        _isOtpSent = false;
        _otpTimeLeft = 0;
      } else {
        throw Exception('Invalid OTP');
      }
      
    } catch (e) {
      _otpError = e.toString();
    } finally {
      _isVerifyingOtp = false;
      notifyListeners();
    }
  }

  void _startOtpTimer() {
    Future.delayed(const Duration(seconds: 1), () {
      if (_otpTimeLeft > 0) {
        _otpTimeLeft--;
        notifyListeners();
        _startOtpTimer();
      }
    });
  }

  // Document upload methods
  Future<void> uploadDocument(String documentType, String filePath) async {
    _isUploadingDocuments = true;
    _uploadError = null;
    notifyListeners();

    try {
      // TODO: Implement actual document upload API call
      await Future.delayed(const Duration(seconds: 2)); // Simulate upload
      
      _uploadedDocuments[documentType] = true;
      
    } catch (e) {
      _uploadError = e.toString();
    } finally {
      _isUploadingDocuments = false;
      notifyListeners();
    }
  }

  Future<void> submitForVerification() async {
    _isUploadingDocuments = true;
    _uploadError = null;
    notifyListeners();

    try {
      // TODO: Implement actual verification submission API call
      await Future.delayed(const Duration(seconds: 2)); // Simulate API call

      _verificationStatus = VerificationStatus.inReview;

      // Update current doctor's verification status
      if (_appState.currentDoctor != null) {
        final updatedDoctor = _appState.currentDoctor!.copyWith(
          verificationStatus: VerificationStatus.inReview,
        );
        _appState.setCurrentDoctor(updatedDoctor);
      }

    } catch (e) {
      _uploadError = e.toString();
    } finally {
      _isUploadingDocuments = false;
      notifyListeners();
    }
  }

  Future<void> skipVerification() async {
    _isUploadingDocuments = true;
    _uploadError = null;
    notifyListeners();

    try {
      // TODO: Implement actual skip verification API call
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call

      _verificationStatus = VerificationStatus.unverified;

      // Update current doctor's verification status and bio
      if (_appState.currentDoctor != null) {
        final updatedDoctor = _appState.currentDoctor!.copyWith(
          verificationStatus: VerificationStatus.unverified,
          isVerified: false,
          bio: '${_appState.currentDoctor!.bio} [Account not verified - documents not submitted]',
        );
        _appState.setCurrentDoctor(updatedDoctor);
      }

    } catch (e) {
      _uploadError = e.toString();
    } finally {
      _isUploadingDocuments = false;
      notifyListeners();
    }
  }

  // Logout
  void logout() {
    _appState.logout();
    _clearAuthState();
  }

  void _clearAuthState() {
    _isRegistering = false;
    _registrationError = null;
    _verificationStatus = VerificationStatus.pending;
    _isLoggingIn = false;
    _loginError = null;
    _isOtpSent = false;
    _isVerifyingOtp = false;
    _otpError = null;
    _otpTimeLeft = 0;
    _isUploadingDocuments = false;
    _uploadError = null;
    _uploadedDocuments.clear();
    notifyListeners();
  }

  // Clear errors
  void clearRegistrationError() {
    _registrationError = null;
    notifyListeners();
  }

  void clearLoginError() {
    _loginError = null;
    notifyListeners();
  }

  void clearOtpError() {
    _otpError = null;
    notifyListeners();
  }

  void clearUploadError() {
    _uploadError = null;
    notifyListeners();
  }
}
