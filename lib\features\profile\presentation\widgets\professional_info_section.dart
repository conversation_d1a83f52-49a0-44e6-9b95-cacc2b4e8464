import 'package:flutter/material.dart';
import '../../../../shared/models/doctor_model.dart';
import '../../../../shared/widgets/section_card.dart';

class ProfessionalInfoSection extends StatelessWidget {
  final Doctor doctor;

  const ProfessionalInfoSection({
    super.key,
    required this.doctor,
  });

  @override
  Widget build(BuildContext context) {
    return SectionCard(
      title: 'Professional Information',
      icon: Icons.work_outline,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Bio
          if (doctor.bio != null && doctor.bio!.isNotEmpty) ...[
            _buildInfoRow(
              'About',
              doctor.bio!,
              Icons.info_outline,
              isMultiline: true,
            ),
            const SizedBox(height: 16),
          ],
          
          // Medical License
          _buildInfoRow(
            'Medical License',
            doctor.medicalLicenseNumber,
            Icons.badge_outlined,
          ),
          
          const SizedBox(height: 12),
          
          // Specialization
          _buildInfoRow(
            'Specialization',
            doctor.specialization,
            Icons.local_hospital_outlined,
          ),
          
          const SizedBox(height: 12),
          
          // Years of Experience
          _buildInfoRow(
            'Experience',
            '${doctor.yearsOfExperience} years',
            Icons.timeline_outlined,
          ),
          
          const SizedBox(height: 12),
          
          // Languages Spoken
          _buildInfoRow(
            'Languages',
            doctor.languagesSpoken.join(', '),
            Icons.language_outlined,
          ),
          
          const SizedBox(height: 12),
          
          // Consultation Fee
          _buildInfoRow(
            'Consultation Fee',
            '₹${doctor.consultationFee.toStringAsFixed(0)}',
            Icons.currency_rupee_outlined,
          ),
          
          const SizedBox(height: 12),
          
          // Contact Information
          const Divider(),
          const SizedBox(height: 12),
          
          Text(
            'Contact Information',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade700,
            ),
          ),
          
          const SizedBox(height: 12),
          
          _buildInfoRow(
            'Email',
            doctor.email,
            Icons.email_outlined,
          ),
          
          const SizedBox(height: 12),
          
          _buildInfoRow(
            'Phone',
            doctor.phoneNumber,
            Icons.phone_outlined,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(
    String label,
    String value,
    IconData icon, {
    bool isMultiline = false,
  }) {
    return Row(
      crossAxisAlignment: isMultiline ? CrossAxisAlignment.start : CrossAxisAlignment.center,
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            size: 20,
            color: Colors.blue.shade600,
          ),
        ),
        
        const SizedBox(width: 12),
        
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: isMultiline ? null : 1,
                overflow: isMultiline ? null : TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
