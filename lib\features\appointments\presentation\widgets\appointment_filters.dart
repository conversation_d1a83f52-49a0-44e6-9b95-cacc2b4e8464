import 'package:flutter/material.dart';
import '../../../../shared/models/appointment_model.dart';

class AppointmentFilters extends StatelessWidget {
  final AppointmentStatus? selectedFilter;
  final Function(AppointmentStatus?) onFilterChanged;

  const AppointmentFilters({
    super.key,
    required this.selectedFilter,
    required this.onFilterChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Text(
                'Filter by Status:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(width: 12),
              if (selectedFilter != null)
                TextButton(
                  onPressed: () => onFilterChanged(null),
                  child: const Text(
                    'Clear',
                    style: TextStyle(fontSize: 12),
                  ),
                ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildFilterChip(
                  'All',
                  null,
                  Icons.list,
                  Colors.grey,
                ),
                const SizedBox(width: 8),
                _buildFilterChip(
                  'Confirmed',
                  AppointmentStatus.confirmed,
                  Icons.schedule,
                  Colors.blue,
                ),
                const SizedBox(width: 8),
                _buildFilterChip(
                  'In Progress',
                  AppointmentStatus.inProgress,
                  Icons.play_circle_outline,
                  Colors.orange,
                ),
                const SizedBox(width: 8),
                _buildFilterChip(
                  'Completed',
                  AppointmentStatus.completed,
                  Icons.check_circle_outline,
                  Colors.green,
                ),
                const SizedBox(width: 8),
                _buildFilterChip(
                  'Cancelled',
                  AppointmentStatus.cancelled,
                  Icons.cancel_outlined,
                  Colors.red,
                ),
                const SizedBox(width: 8),
                _buildFilterChip(
                  'No Show',
                  AppointmentStatus.noShow,
                  Icons.person_off_outlined,
                  Colors.grey,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(
    String label,
    AppointmentStatus? status,
    IconData icon,
    Color color,
  ) {
    final isSelected = selectedFilter == status;
    
    return FilterChip(
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: isSelected ? Colors.white : color,
          ),
          const SizedBox(width: 6),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: isSelected ? Colors.white : color,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
            ),
          ),
        ],
      ),
      selected: isSelected,
      onSelected: (selected) {
        onFilterChanged(selected ? status : null);
      },
      selectedColor: color,
      backgroundColor: color.withOpacity(0.1),
      side: BorderSide(
        color: isSelected ? color : color.withOpacity(0.3),
      ),
      showCheckmark: false,
    );
  }
}
