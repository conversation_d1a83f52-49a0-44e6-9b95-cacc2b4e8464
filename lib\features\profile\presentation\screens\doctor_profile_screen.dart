import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/providers/app_state.dart';
import '../../../../shared/models/doctor_model.dart';
import '../../../../shared/widgets/custom_app_bar.dart';
import '../widgets/profile_header.dart';
import '../widgets/professional_info_section.dart';
import '../widgets/clinic_info_section.dart';
import '../widgets/qualifications_section.dart';
import '../widgets/working_hours_section.dart';
import '../widgets/achievements_section.dart';

class DoctorProfileScreen extends StatefulWidget {
  const DoctorProfileScreen({super.key});

  @override
  State<DoctorProfileScreen> createState() => _DoctorProfileScreenState();
}

class _DoctorProfileScreenState extends State<DoctorProfileScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'My Profile',
        showBackButton: true,
      ),
      body: Consumer<AppState>(
        builder: (context, appState, child) {
          final doctor = appState.currentDoctor;
          
          if (doctor == null) {
            return const Center(
              child: Text('No doctor profile found'),
            );
          }

          return RefreshIndicator(
            onRefresh: () async {
              // TODO: Implement profile refresh
            },
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Profile Header with photo, name, verification status
                  ProfileHeader(doctor: doctor),
                  
                  const SizedBox(height: 24),
                  
                  // Professional Information
                  ProfessionalInfoSection(doctor: doctor),
                  
                  const SizedBox(height: 24),
                  
                  // Clinic Information
                  ClinicInfoSection(doctor: doctor),
                  
                  const SizedBox(height: 24),
                  
                  // Qualifications & Certifications
                  QualificationsSection(doctor: doctor),
                  
                  const SizedBox(height: 24),
                  
                  // Working Hours
                  WorkingHoursSection(doctor: doctor),
                  
                  const SizedBox(height: 24),
                  
                  // Achievements & Badges
                  AchievementsSection(doctor: doctor),
                  
                  const SizedBox(height: 100), // Bottom padding
                ],
              ),
            ),
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.pushNamed(context, '/edit-profile');
        },
        child: const Icon(Icons.edit),
      ),
    );
  }
}
