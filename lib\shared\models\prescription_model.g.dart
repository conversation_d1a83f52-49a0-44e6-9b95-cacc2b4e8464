// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'prescription_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Prescription _$PrescriptionFromJson(Map<String, dynamic> json) => Prescription(
      id: json['id'] as String,
      doctorId: json['doctorId'] as String,
      patientId: json['patientId'] as String,
      appointmentId: json['appointmentId'] as String?,
      doctor: json['doctor'] == null
          ? null
          : Doctor.fromJson(json['doctor'] as Map<String, dynamic>),
      patient: json['patient'] == null
          ? null
          : Patient.fromJson(json['patient'] as Map<String, dynamic>),
      medications: (json['medications'] as List<dynamic>)
          .map((e) => Medication.fromJson(e as Map<String, dynamic>))
          .toList(),
      labTests: (json['labTests'] as List<dynamic>)
          .map((e) => LabTest.fromJson(e as Map<String, dynamic>))
          .toList(),
      diagnosis: json['diagnosis'] as String?,
      symptoms: json['symptoms'] as String?,
      notes: json['notes'] as String?,
      followUpInstructions: json['followUpInstructions'] as String?,
      followUpDate: json['followUpDate'] == null
          ? null
          : DateTime.parse(json['followUpDate'] as String),
      status: $enumDecode(_$PrescriptionStatusEnumMap, json['status']),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      digitalSignature: json['digitalSignature'] as String?,
      isDigitallySigned: json['isDigitallySigned'] as bool,
      attachments: (json['attachments'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$PrescriptionToJson(Prescription instance) =>
    <String, dynamic>{
      'id': instance.id,
      'doctorId': instance.doctorId,
      'patientId': instance.patientId,
      'appointmentId': instance.appointmentId,
      'doctor': instance.doctor,
      'patient': instance.patient,
      'medications': instance.medications,
      'labTests': instance.labTests,
      'diagnosis': instance.diagnosis,
      'symptoms': instance.symptoms,
      'notes': instance.notes,
      'followUpInstructions': instance.followUpInstructions,
      'followUpDate': instance.followUpDate?.toIso8601String(),
      'status': _$PrescriptionStatusEnumMap[instance.status]!,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'digitalSignature': instance.digitalSignature,
      'isDigitallySigned': instance.isDigitallySigned,
      'attachments': instance.attachments,
    };

const _$PrescriptionStatusEnumMap = {
  PrescriptionStatus.draft: 'draft',
  PrescriptionStatus.sent: 'sent',
  PrescriptionStatus.viewed: 'viewed',
  PrescriptionStatus.partiallyFilled: 'partially_filled',
  PrescriptionStatus.filled: 'filled',
  PrescriptionStatus.expired: 'expired',
};

Medication _$MedicationFromJson(Map<String, dynamic> json) => Medication(
      id: json['id'] as String,
      name: json['name'] as String,
      genericName: json['genericName'] as String?,
      dosage: json['dosage'] as String,
      frequency: json['frequency'] as String,
      duration: json['duration'] as String,
      instructions: json['instructions'] as String?,
      type: $enumDecode(_$MedicationTypeEnumMap, json['type']),
      beforeAfterMeal: json['beforeAfterMeal'] as String?,
      quantity: (json['quantity'] as num).toInt(),
      unit: json['unit'] as String?,
      isSubstitutable: json['isSubstitutable'] as bool,
      sideEffects: (json['sideEffects'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      contraindications: (json['contraindications'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      status: $enumDecode(_$MedicationStatusEnumMap, json['status']),
    );

Map<String, dynamic> _$MedicationToJson(Medication instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'genericName': instance.genericName,
      'dosage': instance.dosage,
      'frequency': instance.frequency,
      'duration': instance.duration,
      'instructions': instance.instructions,
      'type': _$MedicationTypeEnumMap[instance.type]!,
      'beforeAfterMeal': instance.beforeAfterMeal,
      'quantity': instance.quantity,
      'unit': instance.unit,
      'isSubstitutable': instance.isSubstitutable,
      'sideEffects': instance.sideEffects,
      'contraindications': instance.contraindications,
      'status': _$MedicationStatusEnumMap[instance.status]!,
    };

const _$MedicationTypeEnumMap = {
  MedicationType.tablet: 'tablet',
  MedicationType.capsule: 'capsule',
  MedicationType.syrup: 'syrup',
  MedicationType.injection: 'injection',
  MedicationType.drops: 'drops',
  MedicationType.cream: 'cream',
  MedicationType.ointment: 'ointment',
  MedicationType.inhaler: 'inhaler',
  MedicationType.spray: 'spray',
  MedicationType.powder: 'powder',
};

const _$MedicationStatusEnumMap = {
  MedicationStatus.active: 'active',
  MedicationStatus.completed: 'completed',
  MedicationStatus.discontinued: 'discontinued',
  MedicationStatus.onHold: 'on_hold',
};

LabTest _$LabTestFromJson(Map<String, dynamic> json) => LabTest(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      category: $enumDecode(_$LabTestCategoryEnumMap, json['category']),
      isFasting: json['isFasting'] as bool,
      instructions: json['instructions'] as String?,
      status: $enumDecode(_$LabTestStatusEnumMap, json['status']),
      scheduledDate: json['scheduledDate'] == null
          ? null
          : DateTime.parse(json['scheduledDate'] as String),
      resultUrl: json['resultUrl'] as String?,
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$LabTestToJson(LabTest instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'category': _$LabTestCategoryEnumMap[instance.category]!,
      'isFasting': instance.isFasting,
      'instructions': instance.instructions,
      'status': _$LabTestStatusEnumMap[instance.status]!,
      'scheduledDate': instance.scheduledDate?.toIso8601String(),
      'resultUrl': instance.resultUrl,
      'notes': instance.notes,
    };

const _$LabTestCategoryEnumMap = {
  LabTestCategory.blood: 'blood',
  LabTestCategory.urine: 'urine',
  LabTestCategory.imaging: 'imaging',
  LabTestCategory.cardiac: 'cardiac',
  LabTestCategory.pulmonary: 'pulmonary',
  LabTestCategory.neurological: 'neurological',
  LabTestCategory.endocrine: 'endocrine',
  LabTestCategory.other: 'other',
};

const _$LabTestStatusEnumMap = {
  LabTestStatus.ordered: 'ordered',
  LabTestStatus.scheduled: 'scheduled',
  LabTestStatus.inProgress: 'in_progress',
  LabTestStatus.completed: 'completed',
  LabTestStatus.cancelled: 'cancelled',
};
