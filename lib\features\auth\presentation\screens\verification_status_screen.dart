import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../shared/models/models.dart';
import '../providers/auth_provider.dart';
import '../../../dashboard/presentation/screens/dashboard_screen.dart';

class VerificationStatusScreen extends StatefulWidget {
  const VerificationStatusScreen({super.key});

  @override
  State<VerificationStatusScreen> createState() => _VerificationStatusScreenState();
}

class _VerificationStatusScreenState extends State<VerificationStatusScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.2, 0.8, curve: Curves.elasticOut),
    ));

    _animationController.forward();

    // Simulate verification process
    _simulateVerification();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _simulateVerification() {
    Future.delayed(const Duration(seconds: 5), () {
      if (mounted) {
        // Simulate verification completion
        // In a real app, this would be handled by backend notifications
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => const DashboardScreen(),
          ),
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          final verificationStatus = authProvider.verificationStatus;
          
          return SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  const Spacer(),
                  
                  // Status Icon/Animation
                  FadeTransition(
                    opacity: _fadeAnimation,
                    child: ScaleTransition(
                      scale: _scaleAnimation,
                      child: _buildStatusIcon(verificationStatus),
                    ),
                  ),
                  
                  const SizedBox(height: 32),
                  
                  // Status Title
                  FadeTransition(
                    opacity: _fadeAnimation,
                    child: Text(
                      _getStatusTitle(verificationStatus),
                      style: const TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Status Description
                  FadeTransition(
                    opacity: _fadeAnimation,
                    child: Text(
                      _getStatusDescription(verificationStatus),
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey.shade600,
                        height: 1.5,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  
                  const SizedBox(height: 32),
                  
                  // Progress Indicator or Status Details
                  if (verificationStatus == VerificationStatus.pending ||
                      verificationStatus == VerificationStatus.inReview)
                    _buildProgressSection(verificationStatus),
                  
                  if (verificationStatus == VerificationStatus.verified)
                    _buildSuccessSection(),
                  
                  if (verificationStatus == VerificationStatus.rejected)
                    _buildRejectedSection(),
                  
                  const Spacer(),
                  
                  // Action Buttons
                  _buildActionButtons(verificationStatus),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildStatusIcon(VerificationStatus status) {
    switch (status) {
      case VerificationStatus.pending:
      case VerificationStatus.inReview:
        return Container(
          width: 120,
          height: 120,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.orange.shade50,
            border: Border.all(
              color: Colors.orange.shade200,
              width: 2,
            ),
          ),
          child: const Icon(
            Icons.hourglass_empty,
            size: 60,
            color: Colors.orange,
          ),
        );

      case VerificationStatus.verified:
        return Container(
          width: 120,
          height: 120,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.green.shade50,
            border: Border.all(
              color: Colors.green.shade200,
              width: 2,
            ),
          ),
          child: const Icon(
            Icons.verified,
            size: 60,
            color: Colors.green,
          ),
        );

      case VerificationStatus.rejected:
        return Container(
          width: 120,
          height: 120,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.red.shade50,
            border: Border.all(
              color: Colors.red.shade200,
              width: 2,
            ),
          ),
          child: const Icon(
            Icons.cancel,
            size: 60,
            color: Colors.red,
          ),
        );

      case VerificationStatus.unverified:
        return Container(
          width: 120,
          height: 120,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.grey.shade50,
            border: Border.all(
              color: Colors.grey.shade300,
              width: 2,
            ),
          ),
          child: const Icon(
            Icons.warning_amber,
            size: 60,
            color: Colors.grey,
          ),
        );
    }
  }

  Widget _buildProgressSection(VerificationStatus status) {
    return Column(
      children: [
        // Animated progress indicator
        SizedBox(
          width: 60,
          height: 60,
          child: CircularProgressIndicator(
            strokeWidth: 4,
            valueColor: AlwaysStoppedAnimation<Color>(
              Theme.of(context).primaryColor,
            ),
          ),
        ),
        
        const SizedBox(height: 24),
        
        // Timeline
        _buildVerificationTimeline(),
      ],
    );
  }

  Widget _buildVerificationTimeline() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Verification Process',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          _buildTimelineItem(
            icon: Icons.upload_file,
            title: 'Documents Submitted',
            subtitle: 'Your documents have been received',
            isCompleted: true,
          ),
          
          _buildTimelineItem(
            icon: Icons.search,
            title: 'Under Review',
            subtitle: 'Our team is verifying your documents',
            isCompleted: false,
            isActive: true,
          ),
          
          _buildTimelineItem(
            icon: Icons.check_circle,
            title: 'Verification Complete',
            subtitle: 'You will be notified once verified',
            isCompleted: false,
          ),
        ],
      ),
    );
  }

  Widget _buildTimelineItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool isCompleted,
    bool isActive = false,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isCompleted
                  ? Colors.green
                  : isActive
                      ? Theme.of(context).primaryColor
                      : Colors.grey.shade300,
            ),
            child: Icon(
              isCompleted ? Icons.check : icon,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: isCompleted || isActive
                        ? Colors.black87
                        : Colors.grey.shade600,
                  ),
                ),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSuccessSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.green.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.green.shade200),
      ),
      child: Column(
        children: [
          Icon(
            Icons.celebration,
            size: 48,
            color: Colors.green.shade600,
          ),
          const SizedBox(height: 16),
          Text(
            'Welcome to MediAssist!',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.green.shade700,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Your account has been verified successfully. You can now start consulting patients.',
            style: TextStyle(
              fontSize: 14,
              color: Colors.green.shade600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildRejectedSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.red.shade200),
      ),
      child: Column(
        children: [
          Icon(
            Icons.info_outline,
            size: 48,
            color: Colors.red.shade600,
          ),
          const SizedBox(height: 16),
          Text(
            'Verification Issues',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.red.shade700,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'There were issues with your submitted documents. Please check your email for details and resubmit.',
            style: TextStyle(
              fontSize: 14,
              color: Colors.red.shade600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(VerificationStatus status) {
    switch (status) {
      case VerificationStatus.pending:
      case VerificationStatus.inReview:
        return Column(
          children: [
            SizedBox(
              width: double.infinity,
              child: OutlinedButton(
                onPressed: () {
                  // TODO: Implement check status functionality
                },
                child: const Text('Check Status'),
              ),
            ),
            const SizedBox(height: 12),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('Go Back'),
            ),
          ],
        );
      
      case VerificationStatus.verified:
        return SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () {
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(
                  builder: (context) => const DashboardScreen(),
                ),
              );
            },
            child: const Text('Continue to Dashboard'),
          ),
        );
      
      case VerificationStatus.rejected:
        return Column(
          children: [
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                child: const Text('Resubmit Documents'),
              ),
            ),
            const SizedBox(height: 12),
            TextButton(
              onPressed: () {
                // TODO: Implement contact support
              },
              child: const Text('Contact Support'),
            ),
          ],
        );

      case VerificationStatus.unverified:
        return Column(
          children: [
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.pushReplacement(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const DashboardScreen(),
                    ),
                  );
                },
                child: const Text('Continue to Dashboard'),
              ),
            ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Verify Documents Later'),
              ),
            ),
          ],
        );
    }
  }

  String _getStatusTitle(VerificationStatus status) {
    switch (status) {
      case VerificationStatus.pending:
        return 'Documents Submitted!';
      case VerificationStatus.inReview:
        return 'Under Review';
      case VerificationStatus.verified:
        return 'Verification Complete!';
      case VerificationStatus.rejected:
        return 'Verification Failed';
      case VerificationStatus.unverified:
        return 'Account Created!';
    }
  }

  String _getStatusDescription(VerificationStatus status) {
    switch (status) {
      case VerificationStatus.pending:
        return 'Your documents have been submitted successfully and are pending review by our verification team.';
      case VerificationStatus.inReview:
        return 'Our team is currently reviewing your submitted documents. This process usually takes 24-48 hours.';
      case VerificationStatus.verified:
        return 'Congratulations! Your account has been verified successfully. You can now start using all features of the app.';
      case VerificationStatus.rejected:
        return 'Unfortunately, there were issues with your submitted documents. Please check your email for specific feedback and resubmit the required documents.';
      case VerificationStatus.unverified:
        return 'Your account has been created successfully! You chose to skip document verification for now. You can verify your documents later from your profile to gain patient trust and access premium features.';
    }
  }
}
