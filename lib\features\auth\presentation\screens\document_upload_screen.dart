import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:file_picker/file_picker.dart';
import '../../../../core/utils/app_utils.dart';
import '../providers/auth_provider.dart';
import 'verification_status_screen.dart';

class DocumentUploadScreen extends StatefulWidget {
  const DocumentUploadScreen({super.key});

  @override
  State<DocumentUploadScreen> createState() => _DocumentUploadScreenState();
}

class _DocumentUploadScreenState extends State<DocumentUploadScreen> {
  final List<DocumentType> _requiredDocuments = [
    DocumentType(
      id: 'medical_degree',
      name: 'Medical Degree Certificate',
      description: 'Upload your MBBS or equivalent degree certificate',
      icon: Icons.school,
      isRequired: true,
    ),
    DocumentType(
      id: 'medical_license',
      name: 'Medical License',
      description: 'Upload your valid medical practice license',
      icon: Icons.assignment,
      isRequired: true,
    ),
    DocumentType(
      id: 'government_id',
      name: 'Government ID Proof',
      description: 'Upload Aadhaar Card, PAN Card, or Passport',
      icon: Icons.credit_card,
      isRequired: true,
    ),
    DocumentType(
      id: 'specialization_certificate',
      name: 'Specialization Certificate',
      description: 'Upload your specialization/post-graduation certificate (if applicable)',
      icon: Icons.workspace_premium,
      isRequired: false,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Document Upload'),
        centerTitle: true,
        elevation: 0,
      ),
      body: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          return Column(
            children: [
              // Header
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withOpacity(0.1),
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.cloud_upload,
                      size: 60,
                      color: Theme.of(context).primaryColor,
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Upload Required Documents',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Please upload clear scans or photos of the following documents for verification',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),

              // Document list
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.all(20),
                  itemCount: _requiredDocuments.length,
                  itemBuilder: (context, index) {
                    final document = _requiredDocuments[index];
                    final isUploaded = authProvider.uploadedDocuments[document.id] ?? false;
                    
                    return _buildDocumentCard(
                      document: document,
                      isUploaded: isUploaded,
                      isUploading: authProvider.isUploadingDocuments,
                      onUpload: () => _uploadDocument(document),
                    );
                  },
                ),
              ),

              // Submit button
              Container(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    if (authProvider.uploadError != null)
                      Container(
                        margin: const EdgeInsets.only(bottom: 16),
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.red.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.red.shade200),
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.error, color: Colors.red.shade600),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                authProvider.uploadError!,
                                style: TextStyle(color: Colors.red.shade600),
                              ),
                            ),
                          ],
                        ),
                      ),
                    
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _canSubmit(authProvider) && !authProvider.isUploadingDocuments
                            ? () => _submitForVerification(authProvider)
                            : null,
                        child: authProvider.isUploadingDocuments
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(strokeWidth: 2),
                              )
                            : const Text('Submit for Verification'),
                      ),
                    ),

                    const SizedBox(height: 12),

                    // Skip for Now button
                    SizedBox(
                      width: double.infinity,
                      child: OutlinedButton(
                        onPressed: !authProvider.isUploadingDocuments
                            ? () => _showSkipConfirmationDialog(authProvider)
                            : null,
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.orange,
                          side: const BorderSide(color: Colors.orange),
                        ),
                        child: const Text('Skip for Now'),
                      ),
                    ),

                    const SizedBox(height: 8),
                    Text(
                      _canSubmit(authProvider)
                          ? 'All required documents uploaded - ready to submit'
                          : 'Upload required documents or skip to continue with unverified account',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildDocumentCard({
    required DocumentType document,
    required bool isUploaded,
    required bool isUploading,
    required VoidCallback onUpload,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: isUploaded 
                        ? Colors.green.shade50 
                        : Theme.of(context).primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    isUploaded ? Icons.check_circle : document.icon,
                    color: isUploaded 
                        ? Colors.green.shade600 
                        : Theme.of(context).primaryColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              document.name,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          if (document.isRequired)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.red.shade50,
                                borderRadius: BorderRadius.circular(4),
                                border: Border.all(color: Colors.red.shade200),
                              ),
                              child: Text(
                                'Required',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Colors.red.shade600,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        document.description,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: isUploading ? null : onUpload,
                    icon: isUploading
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : Icon(isUploaded ? Icons.refresh : Icons.upload_file),
                    label: Text(isUploaded ? 'Replace' : 'Upload'),
                  ),
                ),
                if (isUploaded) ...[
                  const SizedBox(width: 12),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.green.shade50,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.check_circle,
                          size: 16,
                          color: Colors.green.shade600,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Uploaded',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.green.shade600,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _uploadDocument(DocumentType document) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'jpg', 'jpeg', 'png'],
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        
        // Validate file size (max 10MB)
        if (file.size > 10 * 1024 * 1024) {
          AppUtils.showErrorSnackBar(
            context,
            'File size should not exceed 10MB',
          );
          return;
        }

        final authProvider = Provider.of<AuthProvider>(context, listen: false);
        await authProvider.uploadDocument(document.id, file.path!);

        if (authProvider.uploadError == null) {
          AppUtils.showSuccessSnackBar(
            context,
            '${document.name} uploaded successfully',
          );
        } else {
          AppUtils.showErrorSnackBar(
            context,
            authProvider.uploadError!,
          );
        }
      }
    } catch (e) {
      AppUtils.showErrorSnackBar(
        context,
        'Failed to upload document. Please try again.',
      );
    }
  }

  bool _canSubmit(AuthProvider authProvider) {
    final requiredDocuments = _requiredDocuments.where((doc) => doc.isRequired);
    return requiredDocuments.every(
      (doc) => authProvider.uploadedDocuments[doc.id] ?? false,
    );
  }

  Future<void> _submitForVerification(AuthProvider authProvider) async {
    try {
      await authProvider.submitForVerification();

      if (authProvider.uploadError == null) {
        AppUtils.showSuccessSnackBar(
          context,
          'Documents submitted for verification successfully!',
        );

        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => const VerificationStatusScreen(),
          ),
        );
      } else {
        AppUtils.showErrorSnackBar(
          context,
          authProvider.uploadError!,
        );
      }
    } catch (e) {
      AppUtils.showErrorSnackBar(
        context,
        'Failed to submit documents. Please try again.',
      );
    }
  }

  void _showSkipConfirmationDialog(AuthProvider authProvider) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Skip Document Verification?'),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Are you sure you want to skip document verification?',
                style: TextStyle(fontWeight: FontWeight.w500),
              ),
              SizedBox(height: 12),
              Text(
                '⚠️ Your account will be marked as unverified',
                style: TextStyle(color: Colors.orange),
              ),
              SizedBox(height: 8),
              Text(
                '• Patients may be hesitant to book appointments',
                style: TextStyle(fontSize: 14),
              ),
              SizedBox(height: 4),
              Text(
                '• Limited access to premium features',
                style: TextStyle(fontSize: 14),
              ),
              SizedBox(height: 4),
              Text(
                '• You can verify later from your profile',
                style: TextStyle(fontSize: 14),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _skipVerification(authProvider);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
              child: const Text('Skip for Now'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _skipVerification(AuthProvider authProvider) async {
    try {
      await authProvider.skipVerification();

      if (mounted) {
        AppUtils.showSuccessSnackBar(
          context,
          'Account created successfully! You can verify documents later.',
        );

        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => const VerificationStatusScreen(),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showErrorSnackBar(
          context,
          'Failed to skip verification. Please try again.',
        );
      }
    }
  }
}

class DocumentType {
  final String id;
  final String name;
  final String description;
  final IconData icon;
  final bool isRequired;

  DocumentType({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.isRequired,
  });
}
