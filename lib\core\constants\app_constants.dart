class AppConstants {
  // App Information
  static const String appName = 'MediAssist Doctor Portal';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'Professional Healthcare Solutions';

  // Colors
  static const String primaryColorHex = '#003366';
  static const String secondaryColorHex = '#4CAF50';
  static const String accentColorHex = '#2196F3';
  static const String errorColorHex = '#F44336';
  static const String warningColorHex = '#FF9800';
  static const String successColorHex = '#4CAF50';

  // API Endpoints
  static const String baseUrl = 'https://api.mediassist.com';
  static const String authEndpoint = '/auth';
  static const String doctorEndpoint = '/doctors';
  static const String patientEndpoint = '/patients';
  static const String appointmentEndpoint = '/appointments';
  static const String prescriptionEndpoint = '/prescriptions';
  static const String paymentEndpoint = '/payments';

  // Storage Keys
  static const String userTokenKey = 'user_token';
  static const String userDataKey = 'user_data';
  static const String isLoggedInKey = 'is_logged_in';
  static const String themeKey = 'theme_mode';
  static const String languageKey = 'language';

  // Validation
  static const int minPasswordLength = 6;
  static const int maxPasswordLength = 20;
  static const int otpLength = 6;
  static const int otpTimeoutSeconds = 120;

  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;

  // File Upload
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'gif'];
  static const List<String> allowedDocumentTypes = ['pdf', 'doc', 'docx'];

  // Video Call
  static const String agoraAppId = 'your_agora_app_id';
  static const int maxCallDuration = 60 * 60; // 1 hour in seconds

  // Maps
  static const String googleMapsApiKey = 'your_google_maps_api_key';
  static const double defaultLatitude = 28.6139;
  static const double defaultLongitude = 77.2090;

  // Specializations
  static const List<String> medicalSpecializations = [
    'Cardiology',
    'Dermatology',
    'Endocrinology',
    'Gastroenterology',
    'General Practice',
    'Neurology',
    'Orthopedics',
    'Pediatrics',
    'Psychiatry',
    'Radiology',
    'Oncology',
    'Ophthalmology',
    'ENT',
    'Gynecology',
    'Urology',
  ];

  // Languages
  static const List<String> supportedLanguages = [
    'English',
    'Hindi',
    'Spanish',
    'French',
    'German',
    'Chinese',
    'Japanese',
    'Korean',
    'Arabic',
    'Russian',
    'Portuguese',
    'Italian',
  ];

  // Time Slots
  static const List<String> timeSlots = [
    '09:00 AM',
    '09:30 AM',
    '10:00 AM',
    '10:30 AM',
    '11:00 AM',
    '11:30 AM',
    '12:00 PM',
    '12:30 PM',
    '01:00 PM',
    '01:30 PM',
    '02:00 PM',
    '02:30 PM',
    '03:00 PM',
    '03:30 PM',
    '04:00 PM',
    '04:30 PM',
    '05:00 PM',
    '05:30 PM',
    '06:00 PM',
    '06:30 PM',
    '07:00 PM',
    '07:30 PM',
    '08:00 PM',
  ];

  // Consultation Types
  static const List<String> consultationTypes = [
    'General Consultation',
    'Follow-up',
    'Emergency',
    'Second Opinion',
    'Prescription Renewal',
    'Health Checkup',
  ];

  // Payment Methods
  static const List<String> paymentMethods = [
    'UPI',
    'Credit Card',
    'Debit Card',
    'Net Banking',
    'Wallet',
  ];

  // Notification Types
  static const String notificationTypeAppointment = 'appointment';
  static const String notificationTypePayment = 'payment';
  static const String notificationTypeMessage = 'message';
  static const String notificationTypeReminder = 'reminder';

  // Error Messages
  static const String networkError = 'Network connection error. Please check your internet connection.';
  static const String serverError = 'Server error. Please try again later.';
  static const String unknownError = 'An unknown error occurred. Please try again.';
  static const String validationError = 'Please check your input and try again.';
  static const String authError = 'Authentication failed. Please login again.';

  // Success Messages
  static const String registrationSuccess = 'Registration completed successfully!';
  static const String loginSuccess = 'Login successful!';
  static const String profileUpdateSuccess = 'Profile updated successfully!';
  static const String appointmentBookedSuccess = 'Appointment booked successfully!';
  static const String prescriptionSentSuccess = 'Prescription sent successfully!';
}
