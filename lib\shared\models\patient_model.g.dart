// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'patient_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Patient _$PatientFromJson(Map<String, dynamic> json) => Patient(
      id: json['id'] as String,
      fullName: json['fullName'] as String,
      email: json['email'] as String,
      phoneNumber: json['phoneNumber'] as String,
      dateOfBirth: json['dateOfBirth'] == null
          ? null
          : DateTime.parse(json['dateOfBirth'] as String),
      gender: $enumDecodeNullable(_$GenderEnumMap, json['gender']),
      profileImageUrl: json['profileImageUrl'] as String?,
      address: json['address'] as String?,
      emergencyContactName: json['emergencyContactName'] as String?,
      emergencyContactPhone: json['emergencyContactPhone'] as String?,
      allergies:
          (json['allergies'] as List<dynamic>).map((e) => e as String).toList(),
      chronicConditions: (json['chronicConditions'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      currentMedications: (json['currentMedications'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      bloodGroup: $enumDecodeNullable(_$BloodGroupEnumMap, json['bloodGroup']),
      height: (json['height'] as num?)?.toDouble(),
      weight: (json['weight'] as num?)?.toDouble(),
      medicalHistory: MedicalHistory.fromJson(
          json['medicalHistory'] as Map<String, dynamic>),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$PatientToJson(Patient instance) => <String, dynamic>{
      'id': instance.id,
      'fullName': instance.fullName,
      'email': instance.email,
      'phoneNumber': instance.phoneNumber,
      'dateOfBirth': instance.dateOfBirth?.toIso8601String(),
      'gender': _$GenderEnumMap[instance.gender],
      'profileImageUrl': instance.profileImageUrl,
      'address': instance.address,
      'emergencyContactName': instance.emergencyContactName,
      'emergencyContactPhone': instance.emergencyContactPhone,
      'allergies': instance.allergies,
      'chronicConditions': instance.chronicConditions,
      'currentMedications': instance.currentMedications,
      'bloodGroup': _$BloodGroupEnumMap[instance.bloodGroup],
      'height': instance.height,
      'weight': instance.weight,
      'medicalHistory': instance.medicalHistory,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

const _$GenderEnumMap = {
  Gender.male: 'male',
  Gender.female: 'female',
  Gender.other: 'other',
};

const _$BloodGroupEnumMap = {
  BloodGroup.aPositive: 'A+',
  BloodGroup.aNegative: 'A-',
  BloodGroup.bPositive: 'B+',
  BloodGroup.bNegative: 'B-',
  BloodGroup.abPositive: 'AB+',
  BloodGroup.abNegative: 'AB-',
  BloodGroup.oPositive: 'O+',
  BloodGroup.oNegative: 'O-',
};

MedicalHistory _$MedicalHistoryFromJson(Map<String, dynamic> json) =>
    MedicalHistory(
      pastSurgeries: (json['pastSurgeries'] as List<dynamic>)
          .map((e) => PastSurgery.fromJson(e as Map<String, dynamic>))
          .toList(),
      hospitalizations: (json['hospitalizations'] as List<dynamic>)
          .map((e) => Hospitalization.fromJson(e as Map<String, dynamic>))
          .toList(),
      familyHistory: (json['familyHistory'] as List<dynamic>)
          .map((e) => FamilyHistory.fromJson(e as Map<String, dynamic>))
          .toList(),
      vaccinations: (json['vaccinations'] as List<dynamic>)
          .map((e) => Vaccination.fromJson(e as Map<String, dynamic>))
          .toList(),
      labReports: (json['labReports'] as List<dynamic>)
          .map((e) => LabReport.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$MedicalHistoryToJson(MedicalHistory instance) =>
    <String, dynamic>{
      'pastSurgeries': instance.pastSurgeries,
      'hospitalizations': instance.hospitalizations,
      'familyHistory': instance.familyHistory,
      'vaccinations': instance.vaccinations,
      'labReports': instance.labReports,
    };

PastSurgery _$PastSurgeryFromJson(Map<String, dynamic> json) => PastSurgery(
      surgeryName: json['surgeryName'] as String,
      date: DateTime.parse(json['date'] as String),
      hospital: json['hospital'] as String?,
      surgeon: json['surgeon'] as String?,
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$PastSurgeryToJson(PastSurgery instance) =>
    <String, dynamic>{
      'surgeryName': instance.surgeryName,
      'date': instance.date.toIso8601String(),
      'hospital': instance.hospital,
      'surgeon': instance.surgeon,
      'notes': instance.notes,
    };

Hospitalization _$HospitalizationFromJson(Map<String, dynamic> json) =>
    Hospitalization(
      reason: json['reason'] as String,
      admissionDate: DateTime.parse(json['admissionDate'] as String),
      dischargeDate: json['dischargeDate'] == null
          ? null
          : DateTime.parse(json['dischargeDate'] as String),
      hospital: json['hospital'] as String?,
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$HospitalizationToJson(Hospitalization instance) =>
    <String, dynamic>{
      'reason': instance.reason,
      'admissionDate': instance.admissionDate.toIso8601String(),
      'dischargeDate': instance.dischargeDate?.toIso8601String(),
      'hospital': instance.hospital,
      'notes': instance.notes,
    };

FamilyHistory _$FamilyHistoryFromJson(Map<String, dynamic> json) =>
    FamilyHistory(
      relation: json['relation'] as String,
      condition: json['condition'] as String,
      ageOfOnset: (json['ageOfOnset'] as num?)?.toInt(),
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$FamilyHistoryToJson(FamilyHistory instance) =>
    <String, dynamic>{
      'relation': instance.relation,
      'condition': instance.condition,
      'ageOfOnset': instance.ageOfOnset,
      'notes': instance.notes,
    };

Vaccination _$VaccinationFromJson(Map<String, dynamic> json) => Vaccination(
      vaccineName: json['vaccineName'] as String,
      date: DateTime.parse(json['date'] as String),
      batchNumber: json['batchNumber'] as String?,
      administeredBy: json['administeredBy'] as String?,
    );

Map<String, dynamic> _$VaccinationToJson(Vaccination instance) =>
    <String, dynamic>{
      'vaccineName': instance.vaccineName,
      'date': instance.date.toIso8601String(),
      'batchNumber': instance.batchNumber,
      'administeredBy': instance.administeredBy,
    };

LabReport _$LabReportFromJson(Map<String, dynamic> json) => LabReport(
      testName: json['testName'] as String,
      date: DateTime.parse(json['date'] as String),
      result: json['result'] as String,
      normalRange: json['normalRange'] as String?,
      unit: json['unit'] as String?,
      fileUrl: json['fileUrl'] as String?,
    );

Map<String, dynamic> _$LabReportToJson(LabReport instance) => <String, dynamic>{
      'testName': instance.testName,
      'date': instance.date.toIso8601String(),
      'result': instance.result,
      'normalRange': instance.normalRange,
      'unit': instance.unit,
      'fileUrl': instance.fileUrl,
    };
