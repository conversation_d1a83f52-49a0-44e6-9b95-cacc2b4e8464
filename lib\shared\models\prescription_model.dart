import 'package:json_annotation/json_annotation.dart';
import 'doctor_model.dart';
import 'patient_model.dart';

part 'prescription_model.g.dart';

@JsonSerializable()
class Prescription {
  final String id;
  final String doctorId;
  final String patientId;
  final String? appointmentId;
  final Doctor? doctor;
  final Patient? patient;
  final List<Medication> medications;
  final List<LabTest> labTests;
  final String? diagnosis;
  final String? symptoms;
  final String? notes;
  final String? followUpInstructions;
  final DateTime? followUpDate;
  final PrescriptionStatus status;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? digitalSignature;
  final bool isDigitallySigned;
  final List<String> attachments;

  const Prescription({
    required this.id,
    required this.doctorId,
    required this.patientId,
    this.appointmentId,
    this.doctor,
    this.patient,
    required this.medications,
    required this.labTests,
    this.diagnosis,
    this.symptoms,
    this.notes,
    this.followUpInstructions,
    this.followUpDate,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    this.digitalSignature,
    required this.isDigitallySigned,
    required this.attachments,
  });

  factory Prescription.fromJson(Map<String, dynamic> json) => _$PrescriptionFromJson(json);
  Map<String, dynamic> toJson() => _$PrescriptionToJson(this);

  Prescription copyWith({
    String? id,
    String? doctorId,
    String? patientId,
    String? appointmentId,
    Doctor? doctor,
    Patient? patient,
    List<Medication>? medications,
    List<LabTest>? labTests,
    String? diagnosis,
    String? symptoms,
    String? notes,
    String? followUpInstructions,
    DateTime? followUpDate,
    PrescriptionStatus? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? digitalSignature,
    bool? isDigitallySigned,
    List<String>? attachments,
  }) {
    return Prescription(
      id: id ?? this.id,
      doctorId: doctorId ?? this.doctorId,
      patientId: patientId ?? this.patientId,
      appointmentId: appointmentId ?? this.appointmentId,
      doctor: doctor ?? this.doctor,
      patient: patient ?? this.patient,
      medications: medications ?? this.medications,
      labTests: labTests ?? this.labTests,
      diagnosis: diagnosis ?? this.diagnosis,
      symptoms: symptoms ?? this.symptoms,
      notes: notes ?? this.notes,
      followUpInstructions: followUpInstructions ?? this.followUpInstructions,
      followUpDate: followUpDate ?? this.followUpDate,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      digitalSignature: digitalSignature ?? this.digitalSignature,
      isDigitallySigned: isDigitallySigned ?? this.isDigitallySigned,
      attachments: attachments ?? this.attachments,
    );
  }

  bool get hasFollowUp => followUpDate != null;

  bool get isFollowUpDue {
    if (followUpDate == null) return false;
    return DateTime.now().isAfter(followUpDate!);
  }

  int get totalMedications => medications.length;

  int get totalLabTests => labTests.length;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Prescription && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Prescription{id: $id, doctorId: $doctorId, patientId: $patientId}';
  }
}

@JsonSerializable()
class Medication {
  final String id;
  final String name;
  final String? genericName;
  final String dosage;
  final String frequency;
  final String duration;
  final String? instructions;
  final MedicationType type;
  final String? beforeAfterMeal;
  final int quantity;
  final String? unit;
  final bool isSubstitutable;
  final List<String> sideEffects;
  final List<String> contraindications;
  final MedicationStatus status;

  const Medication({
    required this.id,
    required this.name,
    this.genericName,
    required this.dosage,
    required this.frequency,
    required this.duration,
    this.instructions,
    required this.type,
    this.beforeAfterMeal,
    required this.quantity,
    this.unit,
    required this.isSubstitutable,
    required this.sideEffects,
    required this.contraindications,
    required this.status,
  });

  factory Medication.fromJson(Map<String, dynamic> json) => _$MedicationFromJson(json);
  Map<String, dynamic> toJson() => _$MedicationToJson(this);
}

@JsonSerializable()
class LabTest {
  final String id;
  final String name;
  final String? description;
  final LabTestCategory category;
  final bool isFasting;
  final String? instructions;
  final LabTestStatus status;
  final DateTime? scheduledDate;
  final String? resultUrl;
  final String? notes;

  const LabTest({
    required this.id,
    required this.name,
    this.description,
    required this.category,
    required this.isFasting,
    this.instructions,
    required this.status,
    this.scheduledDate,
    this.resultUrl,
    this.notes,
  });

  factory LabTest.fromJson(Map<String, dynamic> json) => _$LabTestFromJson(json);
  Map<String, dynamic> toJson() => _$LabTestToJson(this);
}

enum PrescriptionStatus {
  @JsonValue('draft')
  draft,
  @JsonValue('sent')
  sent,
  @JsonValue('viewed')
  viewed,
  @JsonValue('partially_filled')
  partiallyFilled,
  @JsonValue('filled')
  filled,
  @JsonValue('expired')
  expired,
}

enum MedicationType {
  @JsonValue('tablet')
  tablet,
  @JsonValue('capsule')
  capsule,
  @JsonValue('syrup')
  syrup,
  @JsonValue('injection')
  injection,
  @JsonValue('drops')
  drops,
  @JsonValue('cream')
  cream,
  @JsonValue('ointment')
  ointment,
  @JsonValue('inhaler')
  inhaler,
  @JsonValue('spray')
  spray,
  @JsonValue('powder')
  powder,
}

enum MedicationStatus {
  @JsonValue('active')
  active,
  @JsonValue('completed')
  completed,
  @JsonValue('discontinued')
  discontinued,
  @JsonValue('on_hold')
  onHold,
}

enum LabTestCategory {
  @JsonValue('blood')
  blood,
  @JsonValue('urine')
  urine,
  @JsonValue('imaging')
  imaging,
  @JsonValue('cardiac')
  cardiac,
  @JsonValue('pulmonary')
  pulmonary,
  @JsonValue('neurological')
  neurological,
  @JsonValue('endocrine')
  endocrine,
  @JsonValue('other')
  other,
}

enum LabTestStatus {
  @JsonValue('ordered')
  ordered,
  @JsonValue('scheduled')
  scheduled,
  @JsonValue('in_progress')
  inProgress,
  @JsonValue('completed')
  completed,
  @JsonValue('cancelled')
  cancelled,
}

// Extensions
extension PrescriptionStatusExtension on PrescriptionStatus {
  String get displayName {
    switch (this) {
      case PrescriptionStatus.draft:
        return 'Draft';
      case PrescriptionStatus.sent:
        return 'Sent';
      case PrescriptionStatus.viewed:
        return 'Viewed';
      case PrescriptionStatus.partiallyFilled:
        return 'Partially Filled';
      case PrescriptionStatus.filled:
        return 'Filled';
      case PrescriptionStatus.expired:
        return 'Expired';
    }
  }
}

extension MedicationTypeExtension on MedicationType {
  String get displayName {
    switch (this) {
      case MedicationType.tablet:
        return 'Tablet';
      case MedicationType.capsule:
        return 'Capsule';
      case MedicationType.syrup:
        return 'Syrup';
      case MedicationType.injection:
        return 'Injection';
      case MedicationType.drops:
        return 'Drops';
      case MedicationType.cream:
        return 'Cream';
      case MedicationType.ointment:
        return 'Ointment';
      case MedicationType.inhaler:
        return 'Inhaler';
      case MedicationType.spray:
        return 'Spray';
      case MedicationType.powder:
        return 'Powder';
    }
  }

  String get icon {
    switch (this) {
      case MedicationType.tablet:
        return '💊';
      case MedicationType.capsule:
        return '💊';
      case MedicationType.syrup:
        return '🍯';
      case MedicationType.injection:
        return '💉';
      case MedicationType.drops:
        return '💧';
      case MedicationType.cream:
        return '🧴';
      case MedicationType.ointment:
        return '🧴';
      case MedicationType.inhaler:
        return '🫁';
      case MedicationType.spray:
        return '💨';
      case MedicationType.powder:
        return '🥄';
    }
  }
}

extension LabTestCategoryExtension on LabTestCategory {
  String get displayName {
    switch (this) {
      case LabTestCategory.blood:
        return 'Blood Test';
      case LabTestCategory.urine:
        return 'Urine Test';
      case LabTestCategory.imaging:
        return 'Imaging';
      case LabTestCategory.cardiac:
        return 'Cardiac';
      case LabTestCategory.pulmonary:
        return 'Pulmonary';
      case LabTestCategory.neurological:
        return 'Neurological';
      case LabTestCategory.endocrine:
        return 'Endocrine';
      case LabTestCategory.other:
        return 'Other';
    }
  }
}
