import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/providers/app_state.dart';
import '../../../../core/utils/app_utils.dart';
import '../providers/dashboard_provider.dart';
import '../widgets/dashboard_header.dart';
import '../widgets/stats_cards.dart';
import '../widgets/quick_actions.dart';
import '../widgets/recent_activities.dart';
import '../../../profile/presentation/screens/doctor_profile_screen.dart';
import '../../../appointments/presentation/screens/appointments_screen.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadDashboardData();
    });
  }

  Future<void> _loadDashboardData() async {
    final dashboardProvider = Provider.of<DashboardProvider>(context, listen: false);
    await dashboardProvider.loadDashboardData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Dashboard'),
        centerTitle: true,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () {
              // TODO: Navigate to notifications
            },
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshDashboard,
          ),
        ],
      ),
      body: Consumer2<AppState, DashboardProvider>(
        builder: (context, appState, dashboardProvider, child) {
          if (dashboardProvider.isLoadingStats && dashboardProvider.isLoadingActivities) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          return RefreshIndicator(
            onRefresh: _refreshDashboard,
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with doctor info
                  DashboardHeader(doctor: appState.currentDoctor),
                  
                  const SizedBox(height: 24),
                  
                  // Stats cards
                  if (dashboardProvider.statsError != null)
                    _buildErrorCard(
                      'Failed to load statistics',
                      dashboardProvider.statsError!,
                      () => dashboardProvider.loadStats(),
                    )
                  else
                    StatsCards(
                      todayAppointments: dashboardProvider.todayAppointments,
                      weeklyEarnings: dashboardProvider.weeklyEarnings,
                      totalPatients: dashboardProvider.totalPatients,
                      averageRating: dashboardProvider.averageRating,
                      isLoading: dashboardProvider.isLoadingStats,
                    ),
                  
                  const SizedBox(height: 24),
                  
                  // Quick actions
                  const Text(
                    'Quick Actions',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  const QuickActions(),
                  
                  const SizedBox(height: 24),
                  
                  // Recent activities
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Recent Activities',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      TextButton(
                        onPressed: () {
                          // TODO: Navigate to all activities
                        },
                        child: const Text('View All'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  
                  if (dashboardProvider.activitiesError != null)
                    _buildErrorCard(
                      'Failed to load activities',
                      dashboardProvider.activitiesError!,
                      () => dashboardProvider.loadRecentActivities(),
                    )
                  else
                    RecentActivities(
                      activities: dashboardProvider.recentActivities,
                      isLoading: dashboardProvider.isLoadingActivities,
                    ),
                  
                  const SizedBox(height: 100), // Bottom padding for navigation
                ],
              ),
            ),
          );
        },
      ),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
          _navigateToPage(index);
        },
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'Dashboard',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.calendar_today),
            label: 'Appointments',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.people),
            label: 'Patients',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: 'Profile',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings),
            label: 'Settings',
          ),
        ],
      ),
    );
  }

  Widget _buildErrorCard(String title, String error, VoidCallback onRetry) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: Colors.red.shade400,
            ),
            const SizedBox(height: 12),
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: onRetry,
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _refreshDashboard() async {
    final dashboardProvider = Provider.of<DashboardProvider>(context, listen: false);
    
    try {
      await dashboardProvider.refresh();
      AppUtils.showSuccessSnackBar(context, 'Dashboard refreshed');
    } catch (e) {
      AppUtils.showErrorSnackBar(context, 'Failed to refresh dashboard');
    }
  }

  void _navigateToPage(int index) {
    switch (index) {
      case 0:
        // Already on dashboard
        break;
      case 1:
        // Navigate to appointments
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const AppointmentsScreen(),
          ),
        );
        break;
      case 2:
        // TODO: Navigate to patients
        AppUtils.showInfoDialog(
          context,
          title: 'Coming Soon',
          message: 'Patients feature is under development',
        );
        break;
      case 3:
        // Navigate to profile
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const DoctorProfileScreen(),
          ),
        );
        break;
      case 4:
        // TODO: Navigate to settings
        AppUtils.showInfoDialog(
          context,
          title: 'Coming Soon',
          message: 'Settings feature is under development',
        );
        break;
    }
  }
}
