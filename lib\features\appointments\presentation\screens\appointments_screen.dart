import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../shared/models/appointment_model.dart';
import '../../../../shared/widgets/custom_app_bar.dart';
import '../providers/appointments_provider.dart';
import '../widgets/appointment_card.dart';
import '../widgets/appointment_filters.dart';
import '../widgets/appointment_stats.dart';

class AppointmentsScreen extends StatefulWidget {
  const AppointmentsScreen({super.key});

  @override
  State<AppointmentsScreen> createState() => _AppointmentsScreenState();
}

class _AppointmentsScreenState extends State<AppointmentsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  AppointmentStatus? _selectedFilter;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadAppointments();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadAppointments() async {
    final provider = Provider.of<AppointmentsProvider>(context, listen: false);
    await provider.loadAppointments();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Appointments',
        showBackButton: true,
      ),
      body: Consumer<AppointmentsProvider>(
        builder: (context, provider, child) {
          return Column(
            children: [
              // Stats Section
              const AppointmentStats(),
              
              // Filters
              AppointmentFilters(
                selectedFilter: _selectedFilter,
                onFilterChanged: (filter) {
                  setState(() {
                    _selectedFilter = filter;
                  });
                  provider.filterAppointments(filter);
                },
              ),
              
              // Tab Bar
              Container(
                color: Colors.white,
                child: TabBar(
                  controller: _tabController,
                  labelColor: Theme.of(context).primaryColor,
                  unselectedLabelColor: Colors.grey,
                  indicatorColor: Theme.of(context).primaryColor,
                  tabs: const [
                    Tab(text: 'Today'),
                    Tab(text: 'Upcoming'),
                    Tab(text: 'Completed'),
                    Tab(text: 'All'),
                  ],
                ),
              ),
              
              // Tab Views
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildAppointmentsList(provider.todayAppointments),
                    _buildAppointmentsList(provider.upcomingAppointments),
                    _buildAppointmentsList(provider.completedAppointments),
                    _buildAppointmentsList(provider.allAppointments),
                  ],
                ),
              ),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // TODO: Navigate to add appointment screen
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Add appointment feature will be implemented'),
            ),
          );
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildAppointmentsList(List<Appointment> appointments) {
    if (appointments.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.calendar_today_outlined,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'No appointments found',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Appointments will appear here when scheduled',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadAppointments,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: appointments.length,
        itemBuilder: (context, index) {
          final appointment = appointments[index];
          return AppointmentCard(
            appointment: appointment,
            onTap: () => _navigateToAppointmentDetails(appointment),
            onStatusChanged: (newStatus) => _updateAppointmentStatus(appointment, newStatus),
          );
        },
      ),
    );
  }

  void _navigateToAppointmentDetails(Appointment appointment) {
    Navigator.pushNamed(
      context,
      '/appointment-details',
      arguments: appointment,
    );
  }

  Future<void> _updateAppointmentStatus(Appointment appointment, AppointmentStatus newStatus) async {
    final provider = Provider.of<AppointmentsProvider>(context, listen: false);
    await provider.updateAppointmentStatus(appointment.id, newStatus);
  }
}
