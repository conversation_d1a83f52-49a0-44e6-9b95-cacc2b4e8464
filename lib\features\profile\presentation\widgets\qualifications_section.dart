import 'package:flutter/material.dart';
import '../../../../shared/models/doctor_model.dart';
import '../../../../shared/widgets/section_card.dart';

class QualificationsSection extends StatelessWidget {
  final Doctor doctor;

  const QualificationsSection({
    super.key,
    required this.doctor,
  });

  @override
  Widget build(BuildContext context) {
    return SectionCard(
      title: 'Qualifications & Certifications',
      icon: Icons.school_outlined,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (doctor.qualifications.isNotEmpty) ...[
            // Qualifications List
            ...doctor.qualifications.map((qualification) => 
              _buildQualificationItem(qualification)
            ).toList(),
          ] else ...[
            // Empty State
            Center(
              child: Column(
                children: [
                  Icon(
                    Icons.school_outlined,
                    size: 48,
                    color: Colors.grey.shade400,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'No qualifications added yet',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          ],
          
          const SizedBox(height: 16),
          
          // Add Qualification Button
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () {
                // TODO: Navigate to add qualification screen
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Add qualification feature will be implemented'),
                  ),
                );
              },
              icon: const Icon(Icons.add),
              label: const Text('Add Qualification'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQualificationItem(String qualification) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.green.shade200,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.green.shade100,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.verified,
              size: 20,
              color: Colors.green.shade700,
            ),
          ),
          
          const SizedBox(width: 12),
          
          Expanded(
            child: Text(
              qualification,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          
          // Edit/Remove buttons
          Builder(
            builder: (context) => PopupMenuButton<String>(
              icon: Icon(
                Icons.more_vert,
                color: Colors.grey.shade600,
              ),
              onSelected: (value) {
                if (value == 'edit') {
                  // TODO: Edit qualification
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Edit qualification feature will be implemented'),
                    ),
                  );
                } else if (value == 'delete') {
                  // TODO: Delete qualification
                  _showDeleteConfirmation(context, qualification);
                }
              },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    Icon(Icons.edit, size: 16),
                    SizedBox(width: 8),
                    Text('Edit'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, size: 16, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Delete', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context, String qualification) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Delete Qualification'),
          content: Text('Are you sure you want to delete "$qualification"?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // TODO: Implement delete functionality
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Delete qualification feature will be implemented'),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('Delete'),
            ),
          ],
        );
      },
    );
  }
}
