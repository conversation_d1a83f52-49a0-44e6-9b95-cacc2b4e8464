import 'package:json_annotation/json_annotation.dart';

part 'doctor_model.g.dart';

@JsonSerializable()
class Doctor {
  final String id;
  final String fullName;
  final String email;
  final String phoneNumber;
  final String medicalLicenseNumber;
  final String specialization;
  final int yearsOfExperience;
  final String clinicAddress;
  final List<String> languagesSpoken;
  final String profileImageUrl;
  final bool isVerified;
  final bool isAvailable;
  final double rating;
  final int totalReviews;
  final double consultationFee;
  final String? bio;
  final List<String> qualifications;
  final WorkingHours workingHours;
  final ClinicLocation? clinicLocation;
  final DateTime createdAt;
  final DateTime updatedAt;
  final VerificationStatus verificationStatus;
  final List<String> badges;

  const Doctor({
    required this.id,
    required this.fullName,
    required this.email,
    required this.phoneNumber,
    required this.medicalLicenseNumber,
    required this.specialization,
    required this.yearsOfExperience,
    required this.clinicAddress,
    required this.languagesSpoken,
    required this.profileImageUrl,
    required this.isVerified,
    required this.isAvailable,
    required this.rating,
    required this.totalReviews,
    required this.consultationFee,
    this.bio,
    required this.qualifications,
    required this.workingHours,
    this.clinicLocation,
    required this.createdAt,
    required this.updatedAt,
    required this.verificationStatus,
    required this.badges,
  });

  factory Doctor.fromJson(Map<String, dynamic> json) => _$DoctorFromJson(json);
  Map<String, dynamic> toJson() => _$DoctorToJson(this);

  Doctor copyWith({
    String? id,
    String? fullName,
    String? email,
    String? phoneNumber,
    String? medicalLicenseNumber,
    String? specialization,
    int? yearsOfExperience,
    String? clinicAddress,
    List<String>? languagesSpoken,
    String? profileImageUrl,
    bool? isVerified,
    bool? isAvailable,
    double? rating,
    int? totalReviews,
    double? consultationFee,
    String? bio,
    List<String>? qualifications,
    WorkingHours? workingHours,
    ClinicLocation? clinicLocation,
    DateTime? createdAt,
    DateTime? updatedAt,
    VerificationStatus? verificationStatus,
    List<String>? badges,
  }) {
    return Doctor(
      id: id ?? this.id,
      fullName: fullName ?? this.fullName,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      medicalLicenseNumber: medicalLicenseNumber ?? this.medicalLicenseNumber,
      specialization: specialization ?? this.specialization,
      yearsOfExperience: yearsOfExperience ?? this.yearsOfExperience,
      clinicAddress: clinicAddress ?? this.clinicAddress,
      languagesSpoken: languagesSpoken ?? this.languagesSpoken,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      isVerified: isVerified ?? this.isVerified,
      isAvailable: isAvailable ?? this.isAvailable,
      rating: rating ?? this.rating,
      totalReviews: totalReviews ?? this.totalReviews,
      consultationFee: consultationFee ?? this.consultationFee,
      bio: bio ?? this.bio,
      qualifications: qualifications ?? this.qualifications,
      workingHours: workingHours ?? this.workingHours,
      clinicLocation: clinicLocation ?? this.clinicLocation,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      verificationStatus: verificationStatus ?? this.verificationStatus,
      badges: badges ?? this.badges,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Doctor && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Doctor{id: $id, fullName: $fullName, specialization: $specialization}';
  }
}

@JsonSerializable()
class WorkingHours {
  final Map<String, DaySchedule> schedule;
  final List<String> holidays;
  final List<TimeSlot> blockedSlots;

  const WorkingHours({
    required this.schedule,
    required this.holidays,
    required this.blockedSlots,
  });

  factory WorkingHours.fromJson(Map<String, dynamic> json) => _$WorkingHoursFromJson(json);
  Map<String, dynamic> toJson() => _$WorkingHoursToJson(this);
}

@JsonSerializable()
class DaySchedule {
  final bool isWorking;
  final String? startTime;
  final String? endTime;
  final String? breakStartTime;
  final String? breakEndTime;

  const DaySchedule({
    required this.isWorking,
    this.startTime,
    this.endTime,
    this.breakStartTime,
    this.breakEndTime,
  });

  factory DaySchedule.fromJson(Map<String, dynamic> json) => _$DayScheduleFromJson(json);
  Map<String, dynamic> toJson() => _$DayScheduleToJson(this);
}

@JsonSerializable()
class TimeSlot {
  final DateTime startTime;
  final DateTime endTime;
  final String reason;

  const TimeSlot({
    required this.startTime,
    required this.endTime,
    required this.reason,
  });

  factory TimeSlot.fromJson(Map<String, dynamic> json) => _$TimeSlotFromJson(json);
  Map<String, dynamic> toJson() => _$TimeSlotToJson(this);
}

@JsonSerializable()
class ClinicLocation {
  final double latitude;
  final double longitude;
  final String address;
  final String? landmark;
  final String? city;
  final String? state;
  final String? country;
  final String? postalCode;

  const ClinicLocation({
    required this.latitude,
    required this.longitude,
    required this.address,
    this.landmark,
    this.city,
    this.state,
    this.country,
    this.postalCode,
  });

  factory ClinicLocation.fromJson(Map<String, dynamic> json) => _$ClinicLocationFromJson(json);
  Map<String, dynamic> toJson() => _$ClinicLocationToJson(this);
}

enum VerificationStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('in_review')
  inReview,
  @JsonValue('verified')
  verified,
  @JsonValue('rejected')
  rejected,
  @JsonValue('unverified')
  unverified,
}

// Extension for VerificationStatus
extension VerificationStatusExtension on VerificationStatus {
  String get displayName {
    switch (this) {
      case VerificationStatus.pending:
        return 'Pending';
      case VerificationStatus.inReview:
        return 'In Review';
      case VerificationStatus.verified:
        return 'Verified';
      case VerificationStatus.rejected:
        return 'Rejected';
      case VerificationStatus.unverified:
        return 'Unverified';
    }
  }

  String get description {
    switch (this) {
      case VerificationStatus.pending:
        return 'Your documents are pending verification';
      case VerificationStatus.inReview:
        return 'Your documents are being reviewed';
      case VerificationStatus.verified:
        return 'Your account is verified';
      case VerificationStatus.rejected:
        return 'Your verification was rejected';
      case VerificationStatus.unverified:
        return 'Account not verified - documents not submitted';
    }
  }
}
