import 'package:flutter/material.dart';
import '../../../../shared/models/doctor_model.dart';
import '../../../../shared/widgets/section_card.dart';

class AchievementsSection extends StatelessWidget {
  final Doctor doctor;

  const AchievementsSection({
    super.key,
    required this.doctor,
  });

  @override
  Widget build(BuildContext context) {
    return SectionCard(
      title: 'Achievements & Badges',
      icon: Icons.emoji_events_outlined,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Badges Grid
          if (doctor.badges.isNotEmpty) ...[
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 3,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
              ),
              itemCount: doctor.badges.length,
              itemBuilder: (context, index) {
                return _buildBadgeItem(doctor.badges[index]);
              },
            ),
          ] else ...[
            // Empty State
            Center(
              child: Column(
                children: [
                  Icon(
                    Icons.emoji_events_outlined,
                    size: 48,
                    color: Colors.grey.shade400,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'No badges earned yet',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Complete more consultations to earn badges',
                    style: TextStyle(
                      color: Colors.grey.shade500,
                      fontSize: 12,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
          
          const SizedBox(height: 16),
          
          // Statistics Cards
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Total Reviews',
                  doctor.totalReviews.toString(),
                  Icons.star_outline,
                  Colors.orange,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Rating',
                  '${doctor.rating.toStringAsFixed(1)}/5.0',
                  Icons.thumb_up_outlined,
                  Colors.green,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Experience',
                  '${doctor.yearsOfExperience} Years',
                  Icons.work_outline,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Verification',
                  doctor.verificationStatus.displayName,
                  Icons.verified_outlined,
                  _getVerificationColor(),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBadgeItem(String badge) {
    final badgeInfo = _getBadgeInfo(badge);
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            badgeInfo['color'].withOpacity(0.1),
            badgeInfo['color'].withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: badgeInfo['color'].withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            badgeInfo['icon'],
            size: 20,
            color: badgeInfo['color'],
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              badge,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: badgeInfo['color'],
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.3),
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            size: 24,
            color: color,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Map<String, dynamic> _getBadgeInfo(String badge) {
    // Define badge colors and icons based on badge name
    if (badge.toLowerCase().contains('star') || badge.toLowerCase().contains('top')) {
      return {'color': Colors.amber, 'icon': Icons.star};
    } else if (badge.toLowerCase().contains('expert') || badge.toLowerCase().contains('specialist')) {
      return {'color': Colors.purple, 'icon': Icons.psychology};
    } else if (badge.toLowerCase().contains('trusted') || badge.toLowerCase().contains('reliable')) {
      return {'color': Colors.green, 'icon': Icons.verified};
    } else if (badge.toLowerCase().contains('popular') || badge.toLowerCase().contains('favorite')) {
      return {'color': Colors.pink, 'icon': Icons.favorite};
    } else {
      return {'color': Colors.blue, 'icon': Icons.emoji_events};
    }
  }

  Color _getVerificationColor() {
    switch (doctor.verificationStatus) {
      case VerificationStatus.verified:
        return Colors.green;
      case VerificationStatus.pending:
      case VerificationStatus.inReview:
        return Colors.orange;
      case VerificationStatus.rejected:
        return Colors.red;
      case VerificationStatus.unverified:
        return Colors.grey;
    }
  }
}
