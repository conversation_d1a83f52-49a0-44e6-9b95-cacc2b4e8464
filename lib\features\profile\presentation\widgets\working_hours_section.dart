import 'package:flutter/material.dart';
import '../../../../shared/models/doctor_model.dart';
import '../../../../shared/widgets/section_card.dart';

class WorkingHoursSection extends StatelessWidget {
  final Doctor doctor;

  const WorkingHoursSection({
    super.key,
    required this.doctor,
  });

  @override
  Widget build(BuildContext context) {
    return SectionCard(
      title: 'Working Hours',
      icon: Icons.access_time,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Weekly Schedule
          if (doctor.workingHours.schedule.isNotEmpty) ...[
            ...doctor.workingHours.schedule.entries.map((entry) =>
              _buildDaySchedule(entry.key, _formatDaySchedule(entry.value))
            ).toList(),
          ] else ...[
            // Default schedule display
            _buildDaySchedule('Monday', '09:00 AM - 06:00 PM'),
            _buildDaySchedule('Tuesday', '09:00 AM - 06:00 PM'),
            _buildDaySchedule('Wednesday', '09:00 AM - 06:00 PM'),
            _buildDaySchedule('Thursday', '09:00 AM - 06:00 PM'),
            _buildDaySchedule('Friday', '09:00 AM - 06:00 PM'),
            _buildDaySchedule('Saturday', '09:00 AM - 02:00 PM'),
            _buildDaySchedule('Sunday', 'Closed'),
          ],
          
          const SizedBox(height: 16),
          
          // Holidays Section
          if (doctor.workingHours.holidays.isNotEmpty) ...[
            const Divider(),
            const SizedBox(height: 12),
            
            Text(
              'Upcoming Holidays',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade700,
              ),
            ),
            
            const SizedBox(height: 12),
            
            ...doctor.workingHours.holidays.take(3).map((holiday) =>
              _buildHolidayItem(holiday)
            ).toList(),
          ],
          
          const SizedBox(height: 16),
          
          // Edit Working Hours Button
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () {
                // TODO: Navigate to edit working hours screen
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Edit working hours feature will be implemented'),
                  ),
                );
              },
              icon: const Icon(Icons.edit),
              label: const Text('Edit Working Hours'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDaySchedule(String day, String hours) {
    final isToday = _isToday(day);
    final isClosed = hours.toLowerCase().contains('closed');
    
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: isToday ? Colors.blue.shade50 : Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: isToday ? Border.all(color: Colors.blue.shade200) : null,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            day,
            style: TextStyle(
              fontSize: 14,
              fontWeight: isToday ? FontWeight.w600 : FontWeight.w500,
              color: isToday ? Colors.blue.shade700 : Colors.grey.shade700,
            ),
          ),
          
          Row(
            children: [
              if (isToday)
                Container(
                  margin: const EdgeInsets.only(right: 8),
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade600,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Text(
                    'Today',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              
              Text(
                hours,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: isClosed ? Colors.red.shade600 : Colors.grey.shade800,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildHolidayItem(String holiday) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.orange.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.orange.shade200),
      ),
      child: Row(
        children: [
          Icon(
            Icons.event_busy,
            size: 16,
            color: Colors.orange.shade600,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              holiday,
              style: TextStyle(
                fontSize: 14,
                color: Colors.orange.shade800,
              ),
            ),
          ),
        ],
      ),
    );
  }

  bool _isToday(String day) {
    final today = DateTime.now();
    final weekdays = [
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
      'Sunday'
    ];

    final todayIndex = today.weekday - 1; // Monday = 0
    return weekdays[todayIndex] == day;
  }

  String _formatDaySchedule(DaySchedule schedule) {
    if (!schedule.isWorking) {
      return 'Closed';
    }

    if (schedule.startTime != null && schedule.endTime != null) {
      return '${schedule.startTime} - ${schedule.endTime}';
    }

    return 'Available';
  }
}
