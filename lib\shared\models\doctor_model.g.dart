// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'doctor_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Doctor _<PERSON>(Map<String, dynamic> json) => Doctor(
      id: json['id'] as String,
      fullName: json['fullName'] as String,
      email: json['email'] as String,
      phoneNumber: json['phoneNumber'] as String,
      medicalLicenseNumber: json['medicalLicenseNumber'] as String,
      specialization: json['specialization'] as String,
      yearsOfExperience: (json['yearsOfExperience'] as num).toInt(),
      clinicAddress: json['clinicAddress'] as String,
      languagesSpoken: (json['languagesSpoken'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      profileImageUrl: json['profileImageUrl'] as String,
      isVerified: json['isVerified'] as bool,
      isAvailable: json['isAvailable'] as bool,
      rating: (json['rating'] as num).toDouble(),
      totalReviews: (json['totalReviews'] as num).toInt(),
      consultationFee: (json['consultationFee'] as num).toDouble(),
      bio: json['bio'] as String?,
      qualifications: (json['qualifications'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      workingHours:
          WorkingHours.fromJson(json['workingHours'] as Map<String, dynamic>),
      clinicLocation: json['clinicLocation'] == null
          ? null
          : ClinicLocation.fromJson(
              json['clinicLocation'] as Map<String, dynamic>),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      verificationStatus:
          $enumDecode(_$VerificationStatusEnumMap, json['verificationStatus']),
      badges:
          (json['badges'] as List<dynamic>).map((e) => e as String).toList(),
    );

Map<String, dynamic> _$DoctorToJson(Doctor instance) => <String, dynamic>{
      'id': instance.id,
      'fullName': instance.fullName,
      'email': instance.email,
      'phoneNumber': instance.phoneNumber,
      'medicalLicenseNumber': instance.medicalLicenseNumber,
      'specialization': instance.specialization,
      'yearsOfExperience': instance.yearsOfExperience,
      'clinicAddress': instance.clinicAddress,
      'languagesSpoken': instance.languagesSpoken,
      'profileImageUrl': instance.profileImageUrl,
      'isVerified': instance.isVerified,
      'isAvailable': instance.isAvailable,
      'rating': instance.rating,
      'totalReviews': instance.totalReviews,
      'consultationFee': instance.consultationFee,
      'bio': instance.bio,
      'qualifications': instance.qualifications,
      'workingHours': instance.workingHours,
      'clinicLocation': instance.clinicLocation,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'verificationStatus':
          _$VerificationStatusEnumMap[instance.verificationStatus]!,
      'badges': instance.badges,
    };

const _$VerificationStatusEnumMap = {
  VerificationStatus.pending: 'pending',
  VerificationStatus.inReview: 'in_review',
  VerificationStatus.verified: 'verified',
  VerificationStatus.rejected: 'rejected',
  VerificationStatus.unverified: 'unverified',
};

WorkingHours _$WorkingHoursFromJson(Map<String, dynamic> json) => WorkingHours(
      schedule: (json['schedule'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, DaySchedule.fromJson(e as Map<String, dynamic>)),
      ),
      holidays:
          (json['holidays'] as List<dynamic>).map((e) => e as String).toList(),
      blockedSlots: (json['blockedSlots'] as List<dynamic>)
          .map((e) => TimeSlot.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$WorkingHoursToJson(WorkingHours instance) =>
    <String, dynamic>{
      'schedule': instance.schedule,
      'holidays': instance.holidays,
      'blockedSlots': instance.blockedSlots,
    };

DaySchedule _$DayScheduleFromJson(Map<String, dynamic> json) => DaySchedule(
      isWorking: json['isWorking'] as bool,
      startTime: json['startTime'] as String?,
      endTime: json['endTime'] as String?,
      breakStartTime: json['breakStartTime'] as String?,
      breakEndTime: json['breakEndTime'] as String?,
    );

Map<String, dynamic> _$DayScheduleToJson(DaySchedule instance) =>
    <String, dynamic>{
      'isWorking': instance.isWorking,
      'startTime': instance.startTime,
      'endTime': instance.endTime,
      'breakStartTime': instance.breakStartTime,
      'breakEndTime': instance.breakEndTime,
    };

TimeSlot _$TimeSlotFromJson(Map<String, dynamic> json) => TimeSlot(
      startTime: DateTime.parse(json['startTime'] as String),
      endTime: DateTime.parse(json['endTime'] as String),
      reason: json['reason'] as String,
    );

Map<String, dynamic> _$TimeSlotToJson(TimeSlot instance) => <String, dynamic>{
      'startTime': instance.startTime.toIso8601String(),
      'endTime': instance.endTime.toIso8601String(),
      'reason': instance.reason,
    };

ClinicLocation _$ClinicLocationFromJson(Map<String, dynamic> json) =>
    ClinicLocation(
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      address: json['address'] as String,
      landmark: json['landmark'] as String?,
      city: json['city'] as String?,
      state: json['state'] as String?,
      country: json['country'] as String?,
      postalCode: json['postalCode'] as String?,
    );

Map<String, dynamic> _$ClinicLocationToJson(ClinicLocation instance) =>
    <String, dynamic>{
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'address': instance.address,
      'landmark': instance.landmark,
      'city': instance.city,
      'state': instance.state,
      'country': instance.country,
      'postalCode': instance.postalCode,
    };
