import 'package:flutter/material.dart';
import '../../../../core/utils/app_utils.dart';

class QuickActions extends StatelessWidget {
  const QuickActions({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                context: context,
                title: 'Set Availability',
                subtitle: 'Manage your schedule',
                icon: Icons.access_time,
                color: Colors.blue,
                onTap: () => _handleSetAvailability(context),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildActionCard(
                context: context,
                title: 'View Patients',
                subtitle: 'Patient management',
                icon: Icons.people,
                color: Colors.green,
                onTap: () => _handleViewPatients(context),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                context: context,
                title: 'Start Video Call',
                subtitle: 'Begin consultation',
                icon: Icons.video_call,
                color: Colors.purple,
                onTap: () => _handleStartVideoCall(context),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildActionCard(
                context: context,
                title: 'Write Prescription',
                subtitle: 'Create prescription',
                icon: Icons.edit_note,
                color: Colors.orange,
                onTap: () => _handleWritePrescription(context),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard({
    required BuildContext context,
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 24,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _handleSetAvailability(BuildContext context) {
    AppUtils.showInfoDialog(
      context,
      title: 'Set Availability',
      message: 'Availability management feature is coming soon!',
    );
  }

  void _handleViewPatients(BuildContext context) {
    AppUtils.showInfoDialog(
      context,
      title: 'View Patients',
      message: 'Patient management feature is coming soon!',
    );
  }

  void _handleStartVideoCall(BuildContext context) {
    AppUtils.showInfoDialog(
      context,
      title: 'Start Video Call',
      message: 'Video consultation feature is coming soon!',
    );
  }

  void _handleWritePrescription(BuildContext context) {
    AppUtils.showInfoDialog(
      context,
      title: 'Write Prescription',
      message: 'Prescription management feature is coming soon!',
    );
  }
}
