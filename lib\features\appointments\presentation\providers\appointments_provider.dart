import 'package:flutter/foundation.dart';
import '../../../../shared/models/models.dart';

class AppointmentsProvider extends ChangeNotifier {
  // Appointments data
  List<Appointment> _appointments = [];
  List<Appointment> _todayAppointments = [];
  List<Appointment> _upcomingAppointments = [];
  List<Appointment> _pastAppointments = [];
  List<Appointment> _completedAppointments = [];
  List<Appointment> _filteredAppointments = [];

  // Calendar data
  DateTime _selectedDate = DateTime.now();
  final Map<DateTime, List<Appointment>> _appointmentsByDate = {};

  // Availability data
  Map<String, DaySchedule> _weeklySchedule = {};
  List<TimeSlot> _blockedSlots = [];

  // Loading states
  bool _isLoadingAppointments = false;
  bool _isLoadingAvailability = false;
  bool _isUpdatingAppointment = false;

  // Error states
  String? _appointmentsError;
  String? _availabilityError;
  String? _updateError;

  // Getters
  List<Appointment> get appointments => _appointments;
  List<Appointment> get allAppointments => _filteredAppointments.isNotEmpty ? _filteredAppointments : _appointments;
  List<Appointment> get todayAppointments => _todayAppointments;
  List<Appointment> get upcomingAppointments => _upcomingAppointments;
  List<Appointment> get pastAppointments => _pastAppointments;
  List<Appointment> get completedAppointments => _completedAppointments;
  DateTime get selectedDate => _selectedDate;
  Map<DateTime, List<Appointment>> get appointmentsByDate => _appointmentsByDate;
  Map<String, DaySchedule> get weeklySchedule => _weeklySchedule;
  List<TimeSlot> get blockedSlots => _blockedSlots;
  bool get isLoadingAppointments => _isLoadingAppointments;
  bool get isLoadingAvailability => _isLoadingAvailability;
  bool get isUpdatingAppointment => _isUpdatingAppointment;
  String? get appointmentsError => _appointmentsError;
  String? get availabilityError => _availabilityError;
  String? get updateError => _updateError;

  // Get appointments for selected date
  List<Appointment> get selectedDateAppointments {
    final dateKey = DateTime(_selectedDate.year, _selectedDate.month, _selectedDate.day);
    return _appointmentsByDate[dateKey] ?? [];
  }

  // Load appointments
  Future<void> loadAppointments() async {
    _isLoadingAppointments = true;
    _appointmentsError = null;
    notifyListeners();

    try {
      // TODO: Implement actual API call
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call

      // Mock data
      final now = DateTime.now();
      _appointments = [
        Appointment(
          id: '1',
          doctorId: 'doc1',
          patientId: 'pat1',
          patient: Patient(
            id: 'pat1',
            fullName: 'John Doe',
            email: '<EMAIL>',
            phoneNumber: '+91 **********',
            allergies: [],
            chronicConditions: [],
            currentMedications: [],
            medicalHistory: MedicalHistory(
              pastSurgeries: [],
              hospitalizations: [],
              familyHistory: [],
              vaccinations: [],
              labReports: [],
            ),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          scheduledDateTime: now.add(const Duration(hours: 2)),
          durationMinutes: 30,
          type: AppointmentType.videoConsultation,
          status: AppointmentStatus.confirmed,
          reason: 'Regular checkup',
          consultationFee: 800.0,
          paymentStatus: AppointmentPaymentStatus.paid,
          attachments: [],
          createdAt: now.subtract(const Duration(days: 1)),
          updatedAt: now.subtract(const Duration(days: 1)),
        ),
        Appointment(
          id: '2',
          doctorId: 'doc1',
          patientId: 'pat2',
          patient: Patient(
            id: 'pat2',
            fullName: 'Sarah Johnson',
            email: '<EMAIL>',
            phoneNumber: '+91 **********',
            allergies: [],
            chronicConditions: [],
            currentMedications: [],
            medicalHistory: MedicalHistory(
              pastSurgeries: [],
              hospitalizations: [],
              familyHistory: [],
              vaccinations: [],
              labReports: [],
            ),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          scheduledDateTime: now.add(const Duration(hours: 4)),
          durationMinutes: 45,
          type: AppointmentType.videoConsultation,
          status: AppointmentStatus.confirmed,
          reason: 'Follow-up consultation',
          consultationFee: 800.0,
          paymentStatus: AppointmentPaymentStatus.paid,
          attachments: [],
          createdAt: now.subtract(const Duration(days: 2)),
          updatedAt: now.subtract(const Duration(days: 2)),
        ),
        Appointment(
          id: '3',
          doctorId: 'doc1',
          patientId: 'pat3',
          patient: Patient(
            id: 'pat3',
            fullName: 'Michael Brown',
            email: '<EMAIL>',
            phoneNumber: '+91 **********',
            allergies: [],
            chronicConditions: [],
            currentMedications: [],
            medicalHistory: MedicalHistory(
              pastSurgeries: [],
              hospitalizations: [],
              familyHistory: [],
              vaccinations: [],
              labReports: [],
            ),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          scheduledDateTime: now.subtract(const Duration(hours: 2)),
          durationMinutes: 30,
          type: AppointmentType.videoConsultation,
          status: AppointmentStatus.completed,
          reason: 'Chest pain consultation',
          consultationFee: 800.0,
          paymentStatus: AppointmentPaymentStatus.paid,
          attachments: [],
          createdAt: now.subtract(const Duration(days: 3)),
          updatedAt: now.subtract(const Duration(hours: 2)),
          rating: AppointmentRating(
            rating: 5,
            review: 'Excellent consultation!',
            ratedAt: DateTime.now(),
            ratedBy: 'patient',
          ),
        ),
      ];

      _categorizeAppointments();
      _organizeAppointmentsByDate();

    } catch (e) {
      _appointmentsError = e.toString();
    } finally {
      _isLoadingAppointments = false;
      notifyListeners();
    }
  }

  // Load availability
  Future<void> loadAvailability() async {
    _isLoadingAvailability = true;
    _availabilityError = null;
    notifyListeners();

    try {
      // TODO: Implement actual API call
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call

      // Mock data
      _weeklySchedule = {
        'monday': const DaySchedule(
          isWorking: true,
          startTime: '09:00',
          endTime: '17:00',
          breakStartTime: '13:00',
          breakEndTime: '14:00',
        ),
        'tuesday': const DaySchedule(
          isWorking: true,
          startTime: '09:00',
          endTime: '17:00',
          breakStartTime: '13:00',
          breakEndTime: '14:00',
        ),
        'wednesday': const DaySchedule(
          isWorking: true,
          startTime: '09:00',
          endTime: '17:00',
          breakStartTime: '13:00',
          breakEndTime: '14:00',
        ),
        'thursday': const DaySchedule(
          isWorking: true,
          startTime: '09:00',
          endTime: '17:00',
          breakStartTime: '13:00',
          breakEndTime: '14:00',
        ),
        'friday': const DaySchedule(
          isWorking: true,
          startTime: '09:00',
          endTime: '17:00',
          breakStartTime: '13:00',
          breakEndTime: '14:00',
        ),
        'saturday': const DaySchedule(
          isWorking: true,
          startTime: '09:00',
          endTime: '13:00',
        ),
        'sunday': const DaySchedule(isWorking: false),
      };

      _blockedSlots = [
        TimeSlot(
          startTime: DateTime.now().add(const Duration(days: 1, hours: 10)),
          endTime: DateTime.now().add(const Duration(days: 1, hours: 11)),
          reason: 'Personal appointment',
        ),
      ];

    } catch (e) {
      _availabilityError = e.toString();
    } finally {
      _isLoadingAvailability = false;
      notifyListeners();
    }
  }

  // Update appointment status
  Future<void> updateAppointmentStatus(String appointmentId, AppointmentStatus status) async {
    _isUpdatingAppointment = true;
    _updateError = null;
    notifyListeners();

    try {
      // TODO: Implement actual API call
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call

      // Update local appointment
      final index = _appointments.indexWhere((apt) => apt.id == appointmentId);
      if (index != -1) {
        _appointments[index] = _appointments[index].copyWith(
          status: status,
          updatedAt: DateTime.now(),
        );
        _categorizeAppointments();
        _organizeAppointmentsByDate();
      }

    } catch (e) {
      _updateError = e.toString();
    } finally {
      _isUpdatingAppointment = false;
      notifyListeners();
    }
  }

  // Set selected date
  void setSelectedDate(DateTime date) {
    _selectedDate = date;
    notifyListeners();
  }

  // Update weekly schedule
  Future<void> updateWeeklySchedule(Map<String, DaySchedule> schedule) async {
    _isLoadingAvailability = true;
    _availabilityError = null;
    notifyListeners();

    try {
      // TODO: Implement actual API call
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call

      _weeklySchedule = schedule;

    } catch (e) {
      _availabilityError = e.toString();
    } finally {
      _isLoadingAvailability = false;
      notifyListeners();
    }
  }

  // Add blocked slot
  Future<void> addBlockedSlot(TimeSlot slot) async {
    try {
      // TODO: Implement actual API call
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call

      _blockedSlots.add(slot);
      notifyListeners();

    } catch (e) {
      _availabilityError = e.toString();
      notifyListeners();
    }
  }

  // Remove blocked slot
  Future<void> removeBlockedSlot(int index) async {
    try {
      // TODO: Implement actual API call
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call

      if (index >= 0 && index < _blockedSlots.length) {
        _blockedSlots.removeAt(index);
        notifyListeners();
      }

    } catch (e) {
      _availabilityError = e.toString();
      notifyListeners();
    }
  }

  // Private methods
  void _categorizeAppointments() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    _todayAppointments = _appointments.where((apt) {
      final aptDate = DateTime(apt.scheduledDateTime.year, apt.scheduledDateTime.month, apt.scheduledDateTime.day);
      return aptDate == today;
    }).toList();

    _upcomingAppointments = _appointments.where((apt) {
      return apt.scheduledDateTime.isAfter(now) && 
             apt.status != AppointmentStatus.cancelled &&
             apt.status != AppointmentStatus.completed;
    }).toList();

    _pastAppointments = _appointments.where((apt) {
      return apt.scheduledDateTime.isBefore(now) ||
             apt.status == AppointmentStatus.completed ||
             apt.status == AppointmentStatus.cancelled;
    }).toList();

    _completedAppointments = _appointments.where((apt) {
      return apt.status == AppointmentStatus.completed;
    }).toList();

    // Sort appointments
    _upcomingAppointments.sort((a, b) => a.scheduledDateTime.compareTo(b.scheduledDateTime));
    _pastAppointments.sort((a, b) => b.scheduledDateTime.compareTo(a.scheduledDateTime));
    _completedAppointments.sort((a, b) => b.scheduledDateTime.compareTo(a.scheduledDateTime));
  }

  void _organizeAppointmentsByDate() {
    _appointmentsByDate.clear();
    for (final appointment in _appointments) {
      final date = DateTime(
        appointment.scheduledDateTime.year,
        appointment.scheduledDateTime.month,
        appointment.scheduledDateTime.day,
      );
      
      if (_appointmentsByDate[date] == null) {
        _appointmentsByDate[date] = [];
      }
      _appointmentsByDate[date]!.add(appointment);
    }
  }

  // Refresh data
  Future<void> refresh() async {
    await Future.wait([
      loadAppointments(),
      loadAvailability(),
    ]);
  }

  // Clear errors
  void clearAppointmentsError() {
    _appointmentsError = null;
    notifyListeners();
  }

  void clearAvailabilityError() {
    _availabilityError = null;
    notifyListeners();
  }

  void clearUpdateError() {
    _updateError = null;
    notifyListeners();
  }

  // Stats getters
  int get todayCompletedCount {
    return _todayAppointments.where((apt) => apt.status == AppointmentStatus.completed).length;
  }

  int get todayPendingCount {
    return _todayAppointments.where((apt) =>
      apt.status == AppointmentStatus.confirmed ||
      apt.status == AppointmentStatus.inProgress
    ).length;
  }

  double get todayRevenue {
    return _todayAppointments
        .where((apt) => apt.status == AppointmentStatus.completed)
        .fold(0.0, (sum, apt) => sum + apt.consultationFee);
  }

  double get todayCompletionPercentage {
    if (_todayAppointments.isEmpty) return 0.0;
    return (todayCompletedCount / _todayAppointments.length) * 100;
  }

  // Filter appointments by status
  void filterAppointments(AppointmentStatus? status) {
    if (status == null) {
      _filteredAppointments = [];
    } else {
      _filteredAppointments = _appointments.where((apt) => apt.status == status).toList();
    }
    notifyListeners();
  }

}
