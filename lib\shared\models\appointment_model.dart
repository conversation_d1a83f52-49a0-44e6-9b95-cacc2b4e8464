import 'package:json_annotation/json_annotation.dart';
import 'doctor_model.dart';
import 'patient_model.dart';

part 'appointment_model.g.dart';

@JsonSerializable()
class Appointment {
  final String id;
  final String doctorId;
  final String patientId;
  final Doctor? doctor;
  final Patient? patient;
  final DateTime scheduledDateTime;
  final int durationMinutes;
  final AppointmentType type;
  final AppointmentStatus status;
  final String? reason;
  final String? symptoms;
  final double consultationFee;
  final AppointmentPaymentStatus paymentStatus;
  final String? paymentId;
  final String? notes;
  final String? prescription;
  final List<String> attachments;
  final String? meetingLink;
  final String? meetingId;
  final DateTime? actualStartTime;
  final DateTime? actualEndTime;
  final AppointmentRating? rating;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? cancellationReason;
  final DateTime? cancelledAt;
  final String? cancelledBy;

  const Appointment({
    required this.id,
    required this.doctorId,
    required this.patientId,
    this.doctor,
    this.patient,
    required this.scheduledDateTime,
    required this.durationMinutes,
    required this.type,
    required this.status,
    this.reason,
    this.symptoms,
    required this.consultationFee,
    required this.paymentStatus,
    this.paymentId,
    this.notes,
    this.prescription,
    required this.attachments,
    this.meetingLink,
    this.meetingId,
    this.actualStartTime,
    this.actualEndTime,
    this.rating,
    required this.createdAt,
    required this.updatedAt,
    this.cancellationReason,
    this.cancelledAt,
    this.cancelledBy,
  });

  factory Appointment.fromJson(Map<String, dynamic> json) => _$AppointmentFromJson(json);
  Map<String, dynamic> toJson() => _$AppointmentToJson(this);

  Appointment copyWith({
    String? id,
    String? doctorId,
    String? patientId,
    Doctor? doctor,
    Patient? patient,
    DateTime? scheduledDateTime,
    int? durationMinutes,
    AppointmentType? type,
    AppointmentStatus? status,
    String? reason,
    String? symptoms,
    double? consultationFee,
    AppointmentPaymentStatus? paymentStatus,
    String? paymentId,
    String? notes,
    String? prescription,
    List<String>? attachments,
    String? meetingLink,
    String? meetingId,
    DateTime? actualStartTime,
    DateTime? actualEndTime,
    AppointmentRating? rating,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? cancellationReason,
    DateTime? cancelledAt,
    String? cancelledBy,
  }) {
    return Appointment(
      id: id ?? this.id,
      doctorId: doctorId ?? this.doctorId,
      patientId: patientId ?? this.patientId,
      doctor: doctor ?? this.doctor,
      patient: patient ?? this.patient,
      scheduledDateTime: scheduledDateTime ?? this.scheduledDateTime,
      durationMinutes: durationMinutes ?? this.durationMinutes,
      type: type ?? this.type,
      status: status ?? this.status,
      reason: reason ?? this.reason,
      symptoms: symptoms ?? this.symptoms,
      consultationFee: consultationFee ?? this.consultationFee,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      paymentId: paymentId ?? this.paymentId,
      notes: notes ?? this.notes,
      prescription: prescription ?? this.prescription,
      attachments: attachments ?? this.attachments,
      meetingLink: meetingLink ?? this.meetingLink,
      meetingId: meetingId ?? this.meetingId,
      actualStartTime: actualStartTime ?? this.actualStartTime,
      actualEndTime: actualEndTime ?? this.actualEndTime,
      rating: rating ?? this.rating,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      cancellationReason: cancellationReason ?? this.cancellationReason,
      cancelledAt: cancelledAt ?? this.cancelledAt,
      cancelledBy: cancelledBy ?? this.cancelledBy,
    );
  }

  DateTime get endDateTime {
    return scheduledDateTime.add(Duration(minutes: durationMinutes));
  }

  Duration? get actualDuration {
    if (actualStartTime != null && actualEndTime != null) {
      return actualEndTime!.difference(actualStartTime!);
    }
    return null;
  }

  bool get isUpcoming {
    return status == AppointmentStatus.confirmed &&
           scheduledDateTime.isAfter(DateTime.now());
  }

  bool get isToday {
    final now = DateTime.now();
    return scheduledDateTime.year == now.year &&
           scheduledDateTime.month == now.month &&
           scheduledDateTime.day == now.day;
  }

  bool get canJoin {
    final now = DateTime.now();
    final joinWindow = scheduledDateTime.subtract(const Duration(minutes: 15));
    return status == AppointmentStatus.confirmed &&
           now.isAfter(joinWindow) &&
           now.isBefore(endDateTime);
  }

  bool get canCancel {
    final now = DateTime.now();
    final cancelDeadline = scheduledDateTime.subtract(const Duration(hours: 2));
    return (status == AppointmentStatus.confirmed || status == AppointmentStatus.pending) &&
           now.isBefore(cancelDeadline);
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Appointment && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Appointment{id: $id, doctorId: $doctorId, patientId: $patientId, status: $status}';
  }
}

@JsonSerializable()
class AppointmentRating {
  final int rating; // 1-5 stars
  final String? review;
  final DateTime ratedAt;
  final String ratedBy; // patient or doctor

  const AppointmentRating({
    required this.rating,
    this.review,
    required this.ratedAt,
    required this.ratedBy,
  });

  factory AppointmentRating.fromJson(Map<String, dynamic> json) => _$AppointmentRatingFromJson(json);
  Map<String, dynamic> toJson() => _$AppointmentRatingToJson(this);
}

enum AppointmentType {
  @JsonValue('video_consultation')
  videoConsultation,
  @JsonValue('audio_consultation')
  audioConsultation,
  @JsonValue('chat_consultation')
  chatConsultation,
  @JsonValue('in_person')
  inPerson,
}

enum AppointmentStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('confirmed')
  confirmed,
  @JsonValue('in_progress')
  inProgress,
  @JsonValue('completed')
  completed,
  @JsonValue('cancelled')
  cancelled,
  @JsonValue('no_show')
  noShow,
  @JsonValue('rescheduled')
  rescheduled,
}

enum AppointmentPaymentStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('paid')
  paid,
  @JsonValue('failed')
  failed,
  @JsonValue('refunded')
  refunded,
  @JsonValue('partial_refund')
  partialRefund,
}

// Extensions
extension AppointmentTypeExtension on AppointmentType {
  String get displayName {
    switch (this) {
      case AppointmentType.videoConsultation:
        return 'Video Consultation';
      case AppointmentType.audioConsultation:
        return 'Audio Consultation';
      case AppointmentType.chatConsultation:
        return 'Chat Consultation';
      case AppointmentType.inPerson:
        return 'In-Person';
    }
  }

  String get icon {
    switch (this) {
      case AppointmentType.videoConsultation:
        return '📹';
      case AppointmentType.audioConsultation:
        return '📞';
      case AppointmentType.chatConsultation:
        return '💬';
      case AppointmentType.inPerson:
        return '🏥';
    }
  }
}

extension AppointmentStatusExtension on AppointmentStatus {
  String get displayName {
    switch (this) {
      case AppointmentStatus.pending:
        return 'Pending';
      case AppointmentStatus.confirmed:
        return 'Confirmed';
      case AppointmentStatus.inProgress:
        return 'In Progress';
      case AppointmentStatus.completed:
        return 'Completed';
      case AppointmentStatus.cancelled:
        return 'Cancelled';
      case AppointmentStatus.noShow:
        return 'No Show';
      case AppointmentStatus.rescheduled:
        return 'Rescheduled';
    }
  }

  String get description {
    switch (this) {
      case AppointmentStatus.pending:
        return 'Waiting for confirmation';
      case AppointmentStatus.confirmed:
        return 'Appointment confirmed';
      case AppointmentStatus.inProgress:
        return 'Consultation in progress';
      case AppointmentStatus.completed:
        return 'Consultation completed';
      case AppointmentStatus.cancelled:
        return 'Appointment cancelled';
      case AppointmentStatus.noShow:
        return 'Patient did not show up';
      case AppointmentStatus.rescheduled:
        return 'Appointment rescheduled';
    }
  }
}

extension AppointmentPaymentStatusExtension on AppointmentPaymentStatus {
  String get displayName {
    switch (this) {
      case AppointmentPaymentStatus.pending:
        return 'Pending';
      case AppointmentPaymentStatus.paid:
        return 'Paid';
      case AppointmentPaymentStatus.failed:
        return 'Failed';
      case AppointmentPaymentStatus.refunded:
        return 'Refunded';
      case AppointmentPaymentStatus.partialRefund:
        return 'Partial Refund';
    }
  }
}
