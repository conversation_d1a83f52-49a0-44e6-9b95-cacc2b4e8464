abstract class AppException implements Exception {
  final String message;
  final String? code;
  final dynamic details;

  const AppException(this.message, {this.code, this.details});

  @override
  String toString() => message;
}

// Network Exceptions
class NetworkException extends AppException {
  const NetworkException(super.message, {super.code, super.details});
}

class ServerException extends AppException {
  const ServerException(super.message, {super.code, super.details});
}

class TimeoutException extends AppException {
  const TimeoutException(super.message, {super.code, super.details});
}

// Authentication Exceptions
class AuthException extends AppException {
  const AuthException(super.message, {super.code, super.details});
}

class UnauthorizedException extends AppException {
  const UnauthorizedException(super.message, {super.code, super.details});
}

class TokenExpiredException extends AppException {
  const TokenExpiredException(super.message, {super.code, super.details});
}

// Validation Exceptions
class ValidationException extends AppException {
  const ValidationException(super.message, {super.code, super.details});
}

class InvalidInputException extends AppException {
  const InvalidInputException(super.message, {super.code, super.details});
}

// File Exceptions
class FileException extends AppException {
  const FileException(super.message, {super.code, super.details});
}

class FileNotFound extends AppException {
  const FileNotFound(super.message, {super.code, super.details});
}

class FileSizeException extends AppException {
  const FileSizeException(super.message, {super.code, super.details});
}

class FileTypeException extends AppException {
  const FileTypeException(super.message, {super.code, super.details});
}

// Permission Exceptions
class PermissionException extends AppException {
  const PermissionException(super.message, {super.code, super.details});
}

class CameraPermissionException extends AppException {
  const CameraPermissionException(super.message, {super.code, super.details});
}

class MicrophonePermissionException extends AppException {
  const MicrophonePermissionException(super.message, {super.code, super.details});
}

class LocationPermissionException extends AppException {
  const LocationPermissionException(super.message, {super.code, super.details});
}

class StoragePermissionException extends AppException {
  const StoragePermissionException(super.message, {super.code, super.details});
}

// Business Logic Exceptions
class AppointmentException extends AppException {
  const AppointmentException(super.message, {super.code, super.details});
}

class PaymentException extends AppException {
  const PaymentException(super.message, {super.code, super.details});
}

class PrescriptionException extends AppException {
  const PrescriptionException(super.message, {super.code, super.details});
}

class ConsultationException extends AppException {
  const ConsultationException(super.message, {super.code, super.details});
}

// Generic Exceptions
class CacheException extends AppException {
  const CacheException(super.message, {super.code, super.details});
}

class DatabaseException extends AppException {
  const DatabaseException(super.message, {super.code, super.details});
}

class UnknownException extends AppException {
  const UnknownException(super.message, {super.code, super.details});
}

// Exception Handler
class ExceptionHandler {
  static String getErrorMessage(dynamic error) {
    if (error is AppException) {
      return error.message;
    } else if (error is Exception) {
      return error.toString();
    } else {
      return 'An unexpected error occurred';
    }
  }

  static AppException handleError(dynamic error) {
    if (error is AppException) {
      return error;
    } else if (error.toString().contains('SocketException') ||
               error.toString().contains('NetworkException')) {
      return const NetworkException('No internet connection');
    } else if (error.toString().contains('TimeoutException')) {
      return const TimeoutException('Request timeout');
    } else if (error.toString().contains('FormatException')) {
      return const ValidationException('Invalid data format');
    } else {
      return UnknownException(error.toString());
    }
  }

  static String getErrorCode(AppException exception) {
    return exception.code ?? 'UNKNOWN_ERROR';
  }

  static Map<String, dynamic> getErrorDetails(AppException exception) {
    return {
      'message': exception.message,
      'code': exception.code,
      'details': exception.details,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
}
