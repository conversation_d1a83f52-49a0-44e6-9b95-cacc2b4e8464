// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'appointment_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Appointment _$AppointmentFromJson(Map<String, dynamic> json) => Appointment(
      id: json['id'] as String,
      doctorId: json['doctorId'] as String,
      patientId: json['patientId'] as String,
      doctor: json['doctor'] == null
          ? null
          : Doctor.fromJson(json['doctor'] as Map<String, dynamic>),
      patient: json['patient'] == null
          ? null
          : Patient.fromJson(json['patient'] as Map<String, dynamic>),
      scheduledDateTime: DateTime.parse(json['scheduledDateTime'] as String),
      durationMinutes: (json['durationMinutes'] as num).toInt(),
      type: $enumDecode(_$AppointmentTypeEnumMap, json['type']),
      status: $enumDecode(_$AppointmentStatusEnumMap, json['status']),
      reason: json['reason'] as String?,
      symptoms: json['symptoms'] as String?,
      consultationFee: (json['consultationFee'] as num).toDouble(),
      paymentStatus:
          $enumDecode(_$AppointmentPaymentStatusEnumMap, json['paymentStatus']),
      paymentId: json['paymentId'] as String?,
      notes: json['notes'] as String?,
      prescription: json['prescription'] as String?,
      attachments: (json['attachments'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      meetingLink: json['meetingLink'] as String?,
      meetingId: json['meetingId'] as String?,
      actualStartTime: json['actualStartTime'] == null
          ? null
          : DateTime.parse(json['actualStartTime'] as String),
      actualEndTime: json['actualEndTime'] == null
          ? null
          : DateTime.parse(json['actualEndTime'] as String),
      rating: json['rating'] == null
          ? null
          : AppointmentRating.fromJson(json['rating'] as Map<String, dynamic>),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      cancellationReason: json['cancellationReason'] as String?,
      cancelledAt: json['cancelledAt'] == null
          ? null
          : DateTime.parse(json['cancelledAt'] as String),
      cancelledBy: json['cancelledBy'] as String?,
    );

Map<String, dynamic> _$AppointmentToJson(Appointment instance) =>
    <String, dynamic>{
      'id': instance.id,
      'doctorId': instance.doctorId,
      'patientId': instance.patientId,
      'doctor': instance.doctor,
      'patient': instance.patient,
      'scheduledDateTime': instance.scheduledDateTime.toIso8601String(),
      'durationMinutes': instance.durationMinutes,
      'type': _$AppointmentTypeEnumMap[instance.type]!,
      'status': _$AppointmentStatusEnumMap[instance.status]!,
      'reason': instance.reason,
      'symptoms': instance.symptoms,
      'consultationFee': instance.consultationFee,
      'paymentStatus':
          _$AppointmentPaymentStatusEnumMap[instance.paymentStatus]!,
      'paymentId': instance.paymentId,
      'notes': instance.notes,
      'prescription': instance.prescription,
      'attachments': instance.attachments,
      'meetingLink': instance.meetingLink,
      'meetingId': instance.meetingId,
      'actualStartTime': instance.actualStartTime?.toIso8601String(),
      'actualEndTime': instance.actualEndTime?.toIso8601String(),
      'rating': instance.rating,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'cancellationReason': instance.cancellationReason,
      'cancelledAt': instance.cancelledAt?.toIso8601String(),
      'cancelledBy': instance.cancelledBy,
    };

const _$AppointmentTypeEnumMap = {
  AppointmentType.videoConsultation: 'video_consultation',
  AppointmentType.audioConsultation: 'audio_consultation',
  AppointmentType.chatConsultation: 'chat_consultation',
  AppointmentType.inPerson: 'in_person',
};

const _$AppointmentStatusEnumMap = {
  AppointmentStatus.pending: 'pending',
  AppointmentStatus.confirmed: 'confirmed',
  AppointmentStatus.inProgress: 'in_progress',
  AppointmentStatus.completed: 'completed',
  AppointmentStatus.cancelled: 'cancelled',
  AppointmentStatus.noShow: 'no_show',
  AppointmentStatus.rescheduled: 'rescheduled',
};

const _$AppointmentPaymentStatusEnumMap = {
  AppointmentPaymentStatus.pending: 'pending',
  AppointmentPaymentStatus.paid: 'paid',
  AppointmentPaymentStatus.failed: 'failed',
  AppointmentPaymentStatus.refunded: 'refunded',
  AppointmentPaymentStatus.partialRefund: 'partial_refund',
};

AppointmentRating _$AppointmentRatingFromJson(Map<String, dynamic> json) =>
    AppointmentRating(
      rating: (json['rating'] as num).toInt(),
      review: json['review'] as String?,
      ratedAt: DateTime.parse(json['ratedAt'] as String),
      ratedBy: json['ratedBy'] as String,
    );

Map<String, dynamic> _$AppointmentRatingToJson(AppointmentRating instance) =>
    <String, dynamic>{
      'rating': instance.rating,
      'review': instance.review,
      'ratedAt': instance.ratedAt.toIso8601String(),
      'ratedBy': instance.ratedBy,
    };
