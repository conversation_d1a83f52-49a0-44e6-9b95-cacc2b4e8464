import 'package:json_annotation/json_annotation.dart';

part 'payment_model.g.dart';

@JsonSerializable()
class Payment {
  final String id;
  final String doctorId;
  final String patientId;
  final String? appointmentId;
  final double amount;
  final double platformFee;
  final double doctorEarning;
  final PaymentMethod method;
  final PaymentStatus status;
  final String? transactionId;
  final String? gatewayTransactionId;
  final String? gatewayResponse;
  final DateTime createdAt;
  final DateTime? paidAt;
  final DateTime? refundedAt;
  final String? refundReason;
  final double? refundAmount;
  final String? currency;
  final PaymentGateway gateway;

  const Payment({
    required this.id,
    required this.doctorId,
    required this.patientId,
    this.appointmentId,
    required this.amount,
    required this.platformFee,
    required this.doctorEarning,
    required this.method,
    required this.status,
    this.transactionId,
    this.gatewayTransactionId,
    this.gatewayResponse,
    required this.createdAt,
    this.paidAt,
    this.refundedAt,
    this.refundReason,
    this.refundAmount,
    this.currency = 'INR',
    required this.gateway,
  });

  factory Payment.fromJson(Map<String, dynamic> json) => _$PaymentFromJson(json);
  Map<String, dynamic> toJson() => _$PaymentToJson(this);

  Payment copyWith({
    String? id,
    String? doctorId,
    String? patientId,
    String? appointmentId,
    double? amount,
    double? platformFee,
    double? doctorEarning,
    PaymentMethod? method,
    PaymentStatus? status,
    String? transactionId,
    String? gatewayTransactionId,
    String? gatewayResponse,
    DateTime? createdAt,
    DateTime? paidAt,
    DateTime? refundedAt,
    String? refundReason,
    double? refundAmount,
    String? currency,
    PaymentGateway? gateway,
  }) {
    return Payment(
      id: id ?? this.id,
      doctorId: doctorId ?? this.doctorId,
      patientId: patientId ?? this.patientId,
      appointmentId: appointmentId ?? this.appointmentId,
      amount: amount ?? this.amount,
      platformFee: platformFee ?? this.platformFee,
      doctorEarning: doctorEarning ?? this.doctorEarning,
      method: method ?? this.method,
      status: status ?? this.status,
      transactionId: transactionId ?? this.transactionId,
      gatewayTransactionId: gatewayTransactionId ?? this.gatewayTransactionId,
      gatewayResponse: gatewayResponse ?? this.gatewayResponse,
      createdAt: createdAt ?? this.createdAt,
      paidAt: paidAt ?? this.paidAt,
      refundedAt: refundedAt ?? this.refundedAt,
      refundReason: refundReason ?? this.refundReason,
      refundAmount: refundAmount ?? this.refundAmount,
      currency: currency ?? this.currency,
      gateway: gateway ?? this.gateway,
    );
  }

  bool get isSuccessful => status == PaymentStatus.completed;
  bool get isPending => status == PaymentStatus.pending;
  bool get isFailed => status == PaymentStatus.failed;
  bool get isRefunded => status == PaymentStatus.refunded;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Payment && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Payment{id: $id, amount: $amount, status: $status}';
  }
}

@JsonSerializable()
class Withdrawal {
  final String id;
  final String doctorId;
  final double amount;
  final double processingFee;
  final double netAmount;
  final WithdrawalMethod method;
  final WithdrawalStatus status;
  final BankAccount? bankAccount;
  final UpiDetails? upiDetails;
  final String? transactionId;
  final DateTime requestedAt;
  final DateTime? processedAt;
  final String? rejectionReason;
  final String? notes;

  const Withdrawal({
    required this.id,
    required this.doctorId,
    required this.amount,
    required this.processingFee,
    required this.netAmount,
    required this.method,
    required this.status,
    this.bankAccount,
    this.upiDetails,
    this.transactionId,
    required this.requestedAt,
    this.processedAt,
    this.rejectionReason,
    this.notes,
  });

  factory Withdrawal.fromJson(Map<String, dynamic> json) => _$WithdrawalFromJson(json);
  Map<String, dynamic> toJson() => _$WithdrawalToJson(this);
}

@JsonSerializable()
class BankAccount {
  final String accountNumber;
  final String ifscCode;
  final String accountHolderName;
  final String bankName;
  final String? branchName;

  const BankAccount({
    required this.accountNumber,
    required this.ifscCode,
    required this.accountHolderName,
    required this.bankName,
    this.branchName,
  });

  factory BankAccount.fromJson(Map<String, dynamic> json) => _$BankAccountFromJson(json);
  Map<String, dynamic> toJson() => _$BankAccountToJson(this);
}

@JsonSerializable()
class UpiDetails {
  final String upiId;
  final String? name;

  const UpiDetails({
    required this.upiId,
    this.name,
  });

  factory UpiDetails.fromJson(Map<String, dynamic> json) => _$UpiDetailsFromJson(json);
  Map<String, dynamic> toJson() => _$UpiDetailsToJson(this);
}

@JsonSerializable()
class EarningsSummary {
  final String doctorId;
  final double totalEarnings;
  final double availableBalance;
  final double pendingAmount;
  final double withdrawnAmount;
  final int totalConsultations;
  final int completedConsultations;
  final double averageRating;
  final DateTime periodStart;
  final DateTime periodEnd;
  final List<DailyEarning> dailyEarnings;

  const EarningsSummary({
    required this.doctorId,
    required this.totalEarnings,
    required this.availableBalance,
    required this.pendingAmount,
    required this.withdrawnAmount,
    required this.totalConsultations,
    required this.completedConsultations,
    required this.averageRating,
    required this.periodStart,
    required this.periodEnd,
    required this.dailyEarnings,
  });

  factory EarningsSummary.fromJson(Map<String, dynamic> json) => _$EarningsSummaryFromJson(json);
  Map<String, dynamic> toJson() => _$EarningsSummaryToJson(this);
}

@JsonSerializable()
class DailyEarning {
  final DateTime date;
  final double amount;
  final int consultations;

  const DailyEarning({
    required this.date,
    required this.amount,
    required this.consultations,
  });

  factory DailyEarning.fromJson(Map<String, dynamic> json) => _$DailyEarningFromJson(json);
  Map<String, dynamic> toJson() => _$DailyEarningToJson(this);
}

enum PaymentMethod {
  @JsonValue('upi')
  upi,
  @JsonValue('credit_card')
  creditCard,
  @JsonValue('debit_card')
  debitCard,
  @JsonValue('net_banking')
  netBanking,
  @JsonValue('wallet')
  wallet,
  @JsonValue('cash')
  cash,
}

enum PaymentStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('processing')
  processing,
  @JsonValue('completed')
  completed,
  @JsonValue('failed')
  failed,
  @JsonValue('cancelled')
  cancelled,
  @JsonValue('refunded')
  refunded,
  @JsonValue('partial_refund')
  partialRefund,
}

enum PaymentGateway {
  @JsonValue('razorpay')
  razorpay,
  @JsonValue('payu')
  payu,
  @JsonValue('stripe')
  stripe,
  @JsonValue('phonepe')
  phonepe,
  @JsonValue('gpay')
  gpay,
}

enum WithdrawalMethod {
  @JsonValue('bank_transfer')
  bankTransfer,
  @JsonValue('upi')
  upi,
}

enum WithdrawalStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('processing')
  processing,
  @JsonValue('completed')
  completed,
  @JsonValue('rejected')
  rejected,
  @JsonValue('cancelled')
  cancelled,
}

// Extensions
extension PaymentMethodExtension on PaymentMethod {
  String get displayName {
    switch (this) {
      case PaymentMethod.upi:
        return 'UPI';
      case PaymentMethod.creditCard:
        return 'Credit Card';
      case PaymentMethod.debitCard:
        return 'Debit Card';
      case PaymentMethod.netBanking:
        return 'Net Banking';
      case PaymentMethod.wallet:
        return 'Wallet';
      case PaymentMethod.cash:
        return 'Cash';
    }
  }

  String get icon {
    switch (this) {
      case PaymentMethod.upi:
        return '📱';
      case PaymentMethod.creditCard:
        return '💳';
      case PaymentMethod.debitCard:
        return '💳';
      case PaymentMethod.netBanking:
        return '🏦';
      case PaymentMethod.wallet:
        return '👛';
      case PaymentMethod.cash:
        return '💵';
    }
  }
}

extension PaymentStatusExtension on PaymentStatus {
  String get displayName {
    switch (this) {
      case PaymentStatus.pending:
        return 'Pending';
      case PaymentStatus.processing:
        return 'Processing';
      case PaymentStatus.completed:
        return 'Completed';
      case PaymentStatus.failed:
        return 'Failed';
      case PaymentStatus.cancelled:
        return 'Cancelled';
      case PaymentStatus.refunded:
        return 'Refunded';
      case PaymentStatus.partialRefund:
        return 'Partial Refund';
    }
  }
}

extension WithdrawalStatusExtension on WithdrawalStatus {
  String get displayName {
    switch (this) {
      case WithdrawalStatus.pending:
        return 'Pending';
      case WithdrawalStatus.processing:
        return 'Processing';
      case WithdrawalStatus.completed:
        return 'Completed';
      case WithdrawalStatus.rejected:
        return 'Rejected';
      case WithdrawalStatus.cancelled:
        return 'Cancelled';
    }
  }
}
