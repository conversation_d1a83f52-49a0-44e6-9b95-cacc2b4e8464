import 'package:flutter/foundation.dart';

class DashboardProvider extends ChangeNotifier {
  // Dashboard stats
  int _todayAppointments = 0;
  double _weeklyEarnings = 0.0;
  int _totalPatients = 0;
  double _averageRating = 0.0;
  int _totalReviews = 0;

  // Recent activities
  List<DashboardActivity> _recentActivities = [];

  // Quick stats
  Map<String, int> _quickStats = {};

  // Loading states
  bool _isLoadingStats = false;
  bool _isLoadingActivities = false;

  // Error states
  String? _statsError;
  String? _activitiesError;

  // Getters
  int get todayAppointments => _todayAppointments;
  double get weeklyEarnings => _weeklyEarnings;
  int get totalPatients => _totalPatients;
  double get averageRating => _averageRating;
  int get totalReviews => _totalReviews;
  List<DashboardActivity> get recentActivities => _recentActivities;
  Map<String, int> get quickStats => _quickStats;
  bool get isLoadingStats => _isLoadingStats;
  bool get isLoadingActivities => _isLoadingActivities;
  String? get statsError => _statsError;
  String? get activitiesError => _activitiesError;

  // Load dashboard data
  Future<void> loadDashboardData() async {
    await Future.wait([
      loadStats(),
      loadRecentActivities(),
    ]);
  }

  // Load stats
  Future<void> loadStats() async {
    _isLoadingStats = true;
    _statsError = null;
    notifyListeners();

    try {
      // TODO: Implement actual API call
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call

      // Mock data
      _todayAppointments = 5;
      _weeklyEarnings = 12500.0;
      _totalPatients = 150;
      _averageRating = 4.5;
      _totalReviews = 89;

      _quickStats = {
        'pending_appointments': 3,
        'completed_today': 2,
        'new_patients': 8,
        'pending_prescriptions': 1,
      };

    } catch (e) {
      _statsError = e.toString();
    } finally {
      _isLoadingStats = false;
      notifyListeners();
    }
  }

  // Load recent activities
  Future<void> loadRecentActivities() async {
    _isLoadingActivities = true;
    _activitiesError = null;
    notifyListeners();

    try {
      // TODO: Implement actual API call
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call

      // Mock data
      _recentActivities = [
        DashboardActivity(
          id: '1',
          type: ActivityType.newBooking,
          title: 'New Booking',
          description: 'John Doe - 10:30 AM',
          status: 'Confirmed',
          timestamp: DateTime.now().subtract(const Duration(minutes: 15)),
        ),
        DashboardActivity(
          id: '2',
          type: ActivityType.prescriptionSent,
          title: 'Prescription Sent',
          description: 'Sarah Johnson',
          status: 'Delivered',
          timestamp: DateTime.now().subtract(const Duration(hours: 1)),
        ),
        DashboardActivity(
          id: '3',
          type: ActivityType.feedbackReceived,
          title: 'Feedback Received',
          description: 'Michael Brown - 4.5 ★',
          status: 'View',
          timestamp: DateTime.now().subtract(const Duration(hours: 2)),
        ),
        DashboardActivity(
          id: '4',
          type: ActivityType.paymentReceived,
          title: 'Payment Received',
          description: '₹800 from Emma Wilson',
          status: 'Completed',
          timestamp: DateTime.now().subtract(const Duration(hours: 3)),
        ),
        DashboardActivity(
          id: '5',
          type: ActivityType.appointmentCancelled,
          title: 'Appointment Cancelled',
          description: 'David Lee - 2:00 PM',
          status: 'Refunded',
          timestamp: DateTime.now().subtract(const Duration(hours: 4)),
        ),
      ];

    } catch (e) {
      _activitiesError = e.toString();
    } finally {
      _isLoadingActivities = false;
      notifyListeners();
    }
  }

  // Refresh dashboard
  Future<void> refresh() async {
    await loadDashboardData();
  }

  // Update availability
  Future<void> updateAvailability(bool isAvailable) async {
    try {
      // TODO: Implement actual API call
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call
      
      // Update local state or trigger a refresh
      await refresh();
      
    } catch (e) {
      _statsError = e.toString();
      notifyListeners();
    }
  }

  // Clear errors
  void clearStatsError() {
    _statsError = null;
    notifyListeners();
  }

  void clearActivitiesError() {
    _activitiesError = null;
    notifyListeners();
  }
}

class DashboardActivity {
  final String id;
  final ActivityType type;
  final String title;
  final String description;
  final String status;
  final DateTime timestamp;

  DashboardActivity({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.status,
    required this.timestamp,
  });
}

enum ActivityType {
  newBooking,
  appointmentCancelled,
  prescriptionSent,
  feedbackReceived,
  paymentReceived,
  profileUpdated,
  documentUploaded,
}
