import 'package:flutter/material.dart';
import '../../../../shared/models/doctor_model.dart';
import '../../../../shared/widgets/section_card.dart';

class ClinicInfoSection extends StatelessWidget {
  final Doctor doctor;

  const ClinicInfoSection({
    super.key,
    required this.doctor,
  });

  @override
  Widget build(BuildContext context) {
    return SectionCard(
      title: 'Clinic Information',
      icon: Icons.local_hospital,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Clinic Address
          _buildInfoRow(
            'Address',
            doctor.clinicAddress,
            Icons.location_on_outlined,
            isMultiline: true,
          ),
          
          const SizedBox(height: 16),
          
          // Location Details (if available)
          if (doctor.clinicLocation != null) ...[
            if (doctor.clinicLocation!.city != null)
              _buildInfoRow(
                'City',
                doctor.clinicLocation!.city!,
                Icons.location_city_outlined,
              ),

            if (doctor.clinicLocation!.city != null)
              const SizedBox(height: 12),

            if (doctor.clinicLocation!.state != null)
              _buildInfoRow(
                'State',
                doctor.clinicLocation!.state!,
                Icons.map_outlined,
              ),

            if (doctor.clinicLocation!.state != null)
              const SizedBox(height: 12),

            if (doctor.clinicLocation!.postalCode != null)
              _buildInfoRow(
                'Postal Code',
                doctor.clinicLocation!.postalCode!,
                Icons.pin_drop_outlined,
              ),
            
            const SizedBox(height: 16),
          ],
          
          // Availability Status
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: doctor.isAvailable ? Colors.green.shade50 : Colors.red.shade50,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  doctor.isAvailable ? Icons.check_circle_outline : Icons.cancel_outlined,
                  size: 20,
                  color: doctor.isAvailable ? Colors.green.shade600 : Colors.red.shade600,
                ),
              ),
              
              const SizedBox(width: 12),
              
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Availability',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      doctor.isAvailable ? 'Currently Available' : 'Currently Unavailable',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: doctor.isAvailable ? Colors.green.shade700 : Colors.red.shade700,
                      ),
                    ),
                  ],
                ),
              ),
              
              // Toggle Switch
              Switch(
                value: doctor.isAvailable,
                onChanged: (value) {
                  // TODO: Implement availability toggle
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Availability toggle will be implemented'),
                    ),
                  );
                },
                activeColor: Colors.green,
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Map View Button (if location available)
          if (doctor.clinicLocation != null)
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () {
                  // TODO: Open map view
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Map view will be implemented'),
                    ),
                  );
                },
                icon: const Icon(Icons.map),
                label: const Text('View on Map'),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(
    String label,
    String value,
    IconData icon, {
    bool isMultiline = false,
  }) {
    return Row(
      crossAxisAlignment: isMultiline ? CrossAxisAlignment.start : CrossAxisAlignment.center,
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            size: 20,
            color: Colors.blue.shade600,
          ),
        ),
        
        const SizedBox(width: 12),
        
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: isMultiline ? null : 1,
                overflow: isMultiline ? null : TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
