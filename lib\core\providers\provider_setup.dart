import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

// Core providers
import 'app_state.dart';

// Feature providers
import '../../features/auth/presentation/providers/auth_provider.dart';
import '../../features/dashboard/presentation/providers/dashboard_provider.dart';
import '../../features/appointments/presentation/providers/appointments_provider.dart';

class ProviderSetup {
  static Widget setupProviders({required Widget child}) {
    return MultiProvider(
      providers: [
        // Core providers
        ChangeNotifierProvider<AppState>(
          create: (_) => AppState(),
        ),

        // Auth provider (depends on AppState)
        ChangeNotifierProxyProvider<AppState, AuthProvider>(
          create: (context) => AuthProvider(
            Provider.of<AppState>(context, listen: false),
          ),
          update: (context, appState, previous) => previous ?? AuthProvider(appState),
        ),

        // Dashboard provider
        ChangeNotifierProvider<DashboardProvider>(
          create: (_) => DashboardProvider(),
        ),

        // Appointments provider
        ChangeNotifierProvider<AppointmentsProvider>(
          create: (_) => AppointmentsProvider(),
        ),

        // TODO: Add more providers as they are created
        // - PatientsProvider
        // - ConsultationProvider
        // - PrescriptionProvider
        // - PaymentsProvider
        // - ProfileProvider
        // - AnalyticsProvider
        // - SettingsProvider
      ],
      child: child,
    );
  }

  // Initialize providers that need to load data on app start
  static Future<void> initializeProviders(BuildContext context) async {
    final appState = Provider.of<AppState>(context, listen: false);
    
    // Check if user is already logged in
    // TODO: Implement actual token validation
    // For now, we'll assume user needs to login
    
    if (appState.isAuthenticated) {
      // Load dashboard data if authenticated
      final dashboardProvider = Provider.of<DashboardProvider>(context, listen: false);
      // final appointmentsProvider = Provider.of<AppointmentsProvider>(context, listen: false);

      await Future.wait([
        dashboardProvider.loadDashboardData(),
        // appointmentsProvider.loadAppointments(),
        // appointmentsProvider.loadAvailability(),
      ]);
    }
  }

  // Cleanup providers on app dispose
  static void disposeProviders(BuildContext context) {
    // Any cleanup logic if needed
  }
}
