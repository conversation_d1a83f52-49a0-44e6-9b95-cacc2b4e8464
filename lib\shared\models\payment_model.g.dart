// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Payment _$PaymentFromJson(Map<String, dynamic> json) => Payment(
      id: json['id'] as String,
      doctorId: json['doctorId'] as String,
      patientId: json['patientId'] as String,
      appointmentId: json['appointmentId'] as String?,
      amount: (json['amount'] as num).toDouble(),
      platformFee: (json['platformFee'] as num).toDouble(),
      doctorEarning: (json['doctorEarning'] as num).toDouble(),
      method: $enumDecode(_$PaymentMethodEnumMap, json['method']),
      status: $enumDecode(_$PaymentStatusEnumMap, json['status']),
      transactionId: json['transactionId'] as String?,
      gatewayTransactionId: json['gatewayTransactionId'] as String?,
      gatewayResponse: json['gatewayResponse'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      paidAt: json['paidAt'] == null
          ? null
          : DateTime.parse(json['paidAt'] as String),
      refundedAt: json['refundedAt'] == null
          ? null
          : DateTime.parse(json['refundedAt'] as String),
      refundReason: json['refundReason'] as String?,
      refundAmount: (json['refundAmount'] as num?)?.toDouble(),
      currency: json['currency'] as String? ?? 'INR',
      gateway: $enumDecode(_$PaymentGatewayEnumMap, json['gateway']),
    );

Map<String, dynamic> _$PaymentToJson(Payment instance) => <String, dynamic>{
      'id': instance.id,
      'doctorId': instance.doctorId,
      'patientId': instance.patientId,
      'appointmentId': instance.appointmentId,
      'amount': instance.amount,
      'platformFee': instance.platformFee,
      'doctorEarning': instance.doctorEarning,
      'method': _$PaymentMethodEnumMap[instance.method]!,
      'status': _$PaymentStatusEnumMap[instance.status]!,
      'transactionId': instance.transactionId,
      'gatewayTransactionId': instance.gatewayTransactionId,
      'gatewayResponse': instance.gatewayResponse,
      'createdAt': instance.createdAt.toIso8601String(),
      'paidAt': instance.paidAt?.toIso8601String(),
      'refundedAt': instance.refundedAt?.toIso8601String(),
      'refundReason': instance.refundReason,
      'refundAmount': instance.refundAmount,
      'currency': instance.currency,
      'gateway': _$PaymentGatewayEnumMap[instance.gateway]!,
    };

const _$PaymentMethodEnumMap = {
  PaymentMethod.upi: 'upi',
  PaymentMethod.creditCard: 'credit_card',
  PaymentMethod.debitCard: 'debit_card',
  PaymentMethod.netBanking: 'net_banking',
  PaymentMethod.wallet: 'wallet',
  PaymentMethod.cash: 'cash',
};

const _$PaymentStatusEnumMap = {
  PaymentStatus.pending: 'pending',
  PaymentStatus.processing: 'processing',
  PaymentStatus.completed: 'completed',
  PaymentStatus.failed: 'failed',
  PaymentStatus.cancelled: 'cancelled',
  PaymentStatus.refunded: 'refunded',
  PaymentStatus.partialRefund: 'partial_refund',
};

const _$PaymentGatewayEnumMap = {
  PaymentGateway.razorpay: 'razorpay',
  PaymentGateway.payu: 'payu',
  PaymentGateway.stripe: 'stripe',
  PaymentGateway.phonepe: 'phonepe',
  PaymentGateway.gpay: 'gpay',
};

Withdrawal _$WithdrawalFromJson(Map<String, dynamic> json) => Withdrawal(
      id: json['id'] as String,
      doctorId: json['doctorId'] as String,
      amount: (json['amount'] as num).toDouble(),
      processingFee: (json['processingFee'] as num).toDouble(),
      netAmount: (json['netAmount'] as num).toDouble(),
      method: $enumDecode(_$WithdrawalMethodEnumMap, json['method']),
      status: $enumDecode(_$WithdrawalStatusEnumMap, json['status']),
      bankAccount: json['bankAccount'] == null
          ? null
          : BankAccount.fromJson(json['bankAccount'] as Map<String, dynamic>),
      upiDetails: json['upiDetails'] == null
          ? null
          : UpiDetails.fromJson(json['upiDetails'] as Map<String, dynamic>),
      transactionId: json['transactionId'] as String?,
      requestedAt: DateTime.parse(json['requestedAt'] as String),
      processedAt: json['processedAt'] == null
          ? null
          : DateTime.parse(json['processedAt'] as String),
      rejectionReason: json['rejectionReason'] as String?,
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$WithdrawalToJson(Withdrawal instance) =>
    <String, dynamic>{
      'id': instance.id,
      'doctorId': instance.doctorId,
      'amount': instance.amount,
      'processingFee': instance.processingFee,
      'netAmount': instance.netAmount,
      'method': _$WithdrawalMethodEnumMap[instance.method]!,
      'status': _$WithdrawalStatusEnumMap[instance.status]!,
      'bankAccount': instance.bankAccount,
      'upiDetails': instance.upiDetails,
      'transactionId': instance.transactionId,
      'requestedAt': instance.requestedAt.toIso8601String(),
      'processedAt': instance.processedAt?.toIso8601String(),
      'rejectionReason': instance.rejectionReason,
      'notes': instance.notes,
    };

const _$WithdrawalMethodEnumMap = {
  WithdrawalMethod.bankTransfer: 'bank_transfer',
  WithdrawalMethod.upi: 'upi',
};

const _$WithdrawalStatusEnumMap = {
  WithdrawalStatus.pending: 'pending',
  WithdrawalStatus.processing: 'processing',
  WithdrawalStatus.completed: 'completed',
  WithdrawalStatus.rejected: 'rejected',
  WithdrawalStatus.cancelled: 'cancelled',
};

BankAccount _$BankAccountFromJson(Map<String, dynamic> json) => BankAccount(
      accountNumber: json['accountNumber'] as String,
      ifscCode: json['ifscCode'] as String,
      accountHolderName: json['accountHolderName'] as String,
      bankName: json['bankName'] as String,
      branchName: json['branchName'] as String?,
    );

Map<String, dynamic> _$BankAccountToJson(BankAccount instance) =>
    <String, dynamic>{
      'accountNumber': instance.accountNumber,
      'ifscCode': instance.ifscCode,
      'accountHolderName': instance.accountHolderName,
      'bankName': instance.bankName,
      'branchName': instance.branchName,
    };

UpiDetails _$UpiDetailsFromJson(Map<String, dynamic> json) => UpiDetails(
      upiId: json['upiId'] as String,
      name: json['name'] as String?,
    );

Map<String, dynamic> _$UpiDetailsToJson(UpiDetails instance) =>
    <String, dynamic>{
      'upiId': instance.upiId,
      'name': instance.name,
    };

EarningsSummary _$EarningsSummaryFromJson(Map<String, dynamic> json) =>
    EarningsSummary(
      doctorId: json['doctorId'] as String,
      totalEarnings: (json['totalEarnings'] as num).toDouble(),
      availableBalance: (json['availableBalance'] as num).toDouble(),
      pendingAmount: (json['pendingAmount'] as num).toDouble(),
      withdrawnAmount: (json['withdrawnAmount'] as num).toDouble(),
      totalConsultations: (json['totalConsultations'] as num).toInt(),
      completedConsultations: (json['completedConsultations'] as num).toInt(),
      averageRating: (json['averageRating'] as num).toDouble(),
      periodStart: DateTime.parse(json['periodStart'] as String),
      periodEnd: DateTime.parse(json['periodEnd'] as String),
      dailyEarnings: (json['dailyEarnings'] as List<dynamic>)
          .map((e) => DailyEarning.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$EarningsSummaryToJson(EarningsSummary instance) =>
    <String, dynamic>{
      'doctorId': instance.doctorId,
      'totalEarnings': instance.totalEarnings,
      'availableBalance': instance.availableBalance,
      'pendingAmount': instance.pendingAmount,
      'withdrawnAmount': instance.withdrawnAmount,
      'totalConsultations': instance.totalConsultations,
      'completedConsultations': instance.completedConsultations,
      'averageRating': instance.averageRating,
      'periodStart': instance.periodStart.toIso8601String(),
      'periodEnd': instance.periodEnd.toIso8601String(),
      'dailyEarnings': instance.dailyEarnings,
    };

DailyEarning _$DailyEarningFromJson(Map<String, dynamic> json) => DailyEarning(
      date: DateTime.parse(json['date'] as String),
      amount: (json['amount'] as num).toDouble(),
      consultations: (json['consultations'] as num).toInt(),
    );

Map<String, dynamic> _$DailyEarningToJson(DailyEarning instance) =>
    <String, dynamic>{
      'date': instance.date.toIso8601String(),
      'amount': instance.amount,
      'consultations': instance.consultations,
    };
