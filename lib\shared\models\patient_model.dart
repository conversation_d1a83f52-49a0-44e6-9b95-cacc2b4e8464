import 'package:json_annotation/json_annotation.dart';

part 'patient_model.g.dart';

@JsonSerializable()
class Patient {
  final String id;
  final String fullName;
  final String email;
  final String phoneNumber;
  final DateTime? dateOfBirth;
  final Gender? gender;
  final String? profileImageUrl;
  final String? address;
  final String? emergencyContactName;
  final String? emergencyContactPhone;
  final List<String> allergies;
  final List<String> chronicConditions;
  final List<String> currentMedications;
  final BloodGroup? bloodGroup;
  final double? height; // in cm
  final double? weight; // in kg
  final MedicalHistory medicalHistory;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Patient({
    required this.id,
    required this.fullName,
    required this.email,
    required this.phoneNumber,
    this.dateOfBirth,
    this.gender,
    this.profileImageUrl,
    this.address,
    this.emergencyContactName,
    this.emergencyContactPhone,
    required this.allergies,
    required this.chronicConditions,
    required this.currentMedications,
    this.bloodGroup,
    this.height,
    this.weight,
    required this.medicalHistory,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Patient.fromJson(Map<String, dynamic> json) => _$PatientFromJson(json);
  Map<String, dynamic> toJson() => _$PatientToJson(this);

  Patient copyWith({
    String? id,
    String? fullName,
    String? email,
    String? phoneNumber,
    DateTime? dateOfBirth,
    Gender? gender,
    String? profileImageUrl,
    String? address,
    String? emergencyContactName,
    String? emergencyContactPhone,
    List<String>? allergies,
    List<String>? chronicConditions,
    List<String>? currentMedications,
    BloodGroup? bloodGroup,
    double? height,
    double? weight,
    MedicalHistory? medicalHistory,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Patient(
      id: id ?? this.id,
      fullName: fullName ?? this.fullName,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      address: address ?? this.address,
      emergencyContactName: emergencyContactName ?? this.emergencyContactName,
      emergencyContactPhone: emergencyContactPhone ?? this.emergencyContactPhone,
      allergies: allergies ?? this.allergies,
      chronicConditions: chronicConditions ?? this.chronicConditions,
      currentMedications: currentMedications ?? this.currentMedications,
      bloodGroup: bloodGroup ?? this.bloodGroup,
      height: height ?? this.height,
      weight: weight ?? this.weight,
      medicalHistory: medicalHistory ?? this.medicalHistory,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  int? get age {
    if (dateOfBirth == null) return null;
    final now = DateTime.now();
    int age = now.year - dateOfBirth!.year;
    if (now.month < dateOfBirth!.month ||
        (now.month == dateOfBirth!.month && now.day < dateOfBirth!.day)) {
      age--;
    }
    return age;
  }

  double? get bmi {
    if (height == null || weight == null) return null;
    final heightInMeters = height! / 100;
    return weight! / (heightInMeters * heightInMeters);
  }

  String get bmiCategory {
    final bmiValue = bmi;
    if (bmiValue == null) return 'Unknown';
    
    if (bmiValue < 18.5) return 'Underweight';
    if (bmiValue < 25) return 'Normal';
    if (bmiValue < 30) return 'Overweight';
    return 'Obese';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Patient && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Patient{id: $id, fullName: $fullName, age: $age}';
  }
}

@JsonSerializable()
class MedicalHistory {
  final List<PastSurgery> pastSurgeries;
  final List<Hospitalization> hospitalizations;
  final List<FamilyHistory> familyHistory;
  final List<Vaccination> vaccinations;
  final List<LabReport> labReports;

  const MedicalHistory({
    required this.pastSurgeries,
    required this.hospitalizations,
    required this.familyHistory,
    required this.vaccinations,
    required this.labReports,
  });

  factory MedicalHistory.fromJson(Map<String, dynamic> json) => _$MedicalHistoryFromJson(json);
  Map<String, dynamic> toJson() => _$MedicalHistoryToJson(this);
}

@JsonSerializable()
class PastSurgery {
  final String surgeryName;
  final DateTime date;
  final String? hospital;
  final String? surgeon;
  final String? notes;

  const PastSurgery({
    required this.surgeryName,
    required this.date,
    this.hospital,
    this.surgeon,
    this.notes,
  });

  factory PastSurgery.fromJson(Map<String, dynamic> json) => _$PastSurgeryFromJson(json);
  Map<String, dynamic> toJson() => _$PastSurgeryToJson(this);
}

@JsonSerializable()
class Hospitalization {
  final String reason;
  final DateTime admissionDate;
  final DateTime? dischargeDate;
  final String? hospital;
  final String? notes;

  const Hospitalization({
    required this.reason,
    required this.admissionDate,
    this.dischargeDate,
    this.hospital,
    this.notes,
  });

  factory Hospitalization.fromJson(Map<String, dynamic> json) => _$HospitalizationFromJson(json);
  Map<String, dynamic> toJson() => _$HospitalizationToJson(this);
}

@JsonSerializable()
class FamilyHistory {
  final String relation;
  final String condition;
  final int? ageOfOnset;
  final String? notes;

  const FamilyHistory({
    required this.relation,
    required this.condition,
    this.ageOfOnset,
    this.notes,
  });

  factory FamilyHistory.fromJson(Map<String, dynamic> json) => _$FamilyHistoryFromJson(json);
  Map<String, dynamic> toJson() => _$FamilyHistoryToJson(this);
}

@JsonSerializable()
class Vaccination {
  final String vaccineName;
  final DateTime date;
  final String? batchNumber;
  final String? administeredBy;

  const Vaccination({
    required this.vaccineName,
    required this.date,
    this.batchNumber,
    this.administeredBy,
  });

  factory Vaccination.fromJson(Map<String, dynamic> json) => _$VaccinationFromJson(json);
  Map<String, dynamic> toJson() => _$VaccinationToJson(this);
}

@JsonSerializable()
class LabReport {
  final String testName;
  final DateTime date;
  final String result;
  final String? normalRange;
  final String? unit;
  final String? fileUrl;

  const LabReport({
    required this.testName,
    required this.date,
    required this.result,
    this.normalRange,
    this.unit,
    this.fileUrl,
  });

  factory LabReport.fromJson(Map<String, dynamic> json) => _$LabReportFromJson(json);
  Map<String, dynamic> toJson() => _$LabReportToJson(this);
}

enum Gender {
  @JsonValue('male')
  male,
  @JsonValue('female')
  female,
  @JsonValue('other')
  other,
}

enum BloodGroup {
  @JsonValue('A+')
  aPositive,
  @JsonValue('A-')
  aNegative,
  @JsonValue('B+')
  bPositive,
  @JsonValue('B-')
  bNegative,
  @JsonValue('AB+')
  abPositive,
  @JsonValue('AB-')
  abNegative,
  @JsonValue('O+')
  oPositive,
  @JsonValue('O-')
  oNegative,
}

// Extensions
extension GenderExtension on Gender {
  String get displayName {
    switch (this) {
      case Gender.male:
        return 'Male';
      case Gender.female:
        return 'Female';
      case Gender.other:
        return 'Other';
    }
  }
}

extension BloodGroupExtension on BloodGroup {
  String get displayName {
    switch (this) {
      case BloodGroup.aPositive:
        return 'A+';
      case BloodGroup.aNegative:
        return 'A-';
      case BloodGroup.bPositive:
        return 'B+';
      case BloodGroup.bNegative:
        return 'B-';
      case BloodGroup.abPositive:
        return 'AB+';
      case BloodGroup.abNegative:
        return 'AB-';
      case BloodGroup.oPositive:
        return 'O+';
      case BloodGroup.oNegative:
        return 'O-';
    }
  }
}
