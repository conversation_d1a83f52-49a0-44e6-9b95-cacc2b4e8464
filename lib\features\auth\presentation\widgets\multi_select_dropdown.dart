import 'package:flutter/material.dart';

class MultiSelectDropdown extends StatefulWidget {
  final String label;
  final IconData icon;
  final List<String> items;
  final List<String> selectedItems;
  final void Function(List<String>) onChanged;
  final String? Function(List<String>)? validator;
  final String? hint;

  const MultiSelectDropdown({
    super.key,
    required this.label,
    required this.icon,
    required this.items,
    required this.selectedItems,
    required this.onChanged,
    this.validator,
    this.hint,
  });

  @override
  State<MultiSelectDropdown> createState() => _MultiSelectDropdownState();
}

class _MultiSelectDropdownState extends State<MultiSelectDropdown> {
  bool _isExpanded = false;
  String? _errorText;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.label,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 8),
        
        // Main dropdown container
        Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: _errorText != null 
                  ? Colors.red 
                  : Colors.grey.shade300,
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              // Header (always visible)
              InkWell(
                onTap: () {
                  setState(() {
                    _isExpanded = !_isExpanded;
                  });
                },
                borderRadius: BorderRadius.circular(12),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 16,
                  ),
                  child: Row(
                    children: [
                      Icon(
                        widget.icon,
                        color: Colors.grey.shade600,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: widget.selectedItems.isEmpty
                            ? Text(
                                widget.hint ?? 'Select ${widget.label}',
                                style: TextStyle(
                                  color: Colors.grey.shade600,
                                  fontSize: 16,
                                ),
                              )
                            : Wrap(
                                spacing: 8,
                                runSpacing: 4,
                                children: widget.selectedItems.map((item) {
                                  return Chip(
                                    label: Text(
                                      item,
                                      style: const TextStyle(fontSize: 12),
                                    ),
                                    onDeleted: () {
                                      final newList = List<String>.from(widget.selectedItems);
                                      newList.remove(item);
                                      widget.onChanged(newList);
                                      _validateSelection(newList);
                                    },
                                    deleteIconColor: Colors.grey.shade600,
                                    backgroundColor: Theme.of(context).primaryColor.withOpacity(0.1),
                                    side: BorderSide(
                                      color: Theme.of(context).primaryColor.withOpacity(0.3),
                                    ),
                                  );
                                }).toList(),
                              ),
                      ),
                      Icon(
                        _isExpanded 
                            ? Icons.keyboard_arrow_up 
                            : Icons.keyboard_arrow_down,
                        color: Colors.grey.shade600,
                      ),
                    ],
                  ),
                ),
              ),
              
              // Expandable options list
              if (_isExpanded)
                Container(
                  decoration: BoxDecoration(
                    border: Border(
                      top: BorderSide(color: Colors.grey.shade300),
                    ),
                  ),
                  constraints: const BoxConstraints(maxHeight: 200),
                  child: ListView.builder(
                    shrinkWrap: true,
                    itemCount: widget.items.length,
                    itemBuilder: (context, index) {
                      final item = widget.items[index];
                      final isSelected = widget.selectedItems.contains(item);
                      
                      return CheckboxListTile(
                        value: isSelected,
                        onChanged: (bool? value) {
                          final newList = List<String>.from(widget.selectedItems);
                          if (value == true) {
                            newList.add(item);
                          } else {
                            newList.remove(item);
                          }
                          widget.onChanged(newList);
                          _validateSelection(newList);
                        },
                        title: Text(
                          item,
                          style: const TextStyle(fontSize: 14),
                        ),
                        controlAffinity: ListTileControlAffinity.leading,
                        dense: true,
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 0,
                        ),
                      );
                    },
                  ),
                ),
            ],
          ),
        ),
        
        // Error text
        if (_errorText != null)
          Padding(
            padding: const EdgeInsets.only(top: 8, left: 12),
            child: Text(
              _errorText!,
              style: const TextStyle(
                color: Colors.red,
                fontSize: 12,
              ),
            ),
          ),
      ],
    );
  }

  void _validateSelection(List<String> selection) {
    if (widget.validator != null) {
      setState(() {
        _errorText = widget.validator!(selection);
      });
    }
  }
}
